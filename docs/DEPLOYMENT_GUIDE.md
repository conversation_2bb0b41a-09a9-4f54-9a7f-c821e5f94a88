# Deployment Guide - Candidate Scoring API v2.0.0

This guide provides comprehensive instructions for deploying the refactored candidate scoring and matching API in various environments.

## 📋 Table of Contents

- [Prerequisites](#prerequisites)
- [Environment Setup](#environment-setup)
- [Local Development](#local-development)
- [Staging Deployment](#staging-deployment)
- [Production Deployment](#production-deployment)
- [Docker Deployment](#docker-deployment)
- [Monitoring & Maintenance](#monitoring--maintenance)
- [Troubleshooting](#troubleshooting)

## 🔧 Prerequisites

### System Requirements

- **Operating System**: Linux (Ubuntu 20.04+ recommended), macOS, or Windows
- **R Version**: R 4.0.0 or higher
- **Database**: PostgreSQL 12.0 or higher
- **Memory**: Minimum 2GB RAM (4GB+ recommended for production)
- **Storage**: Minimum 1GB free space
- **Network**: Internet access for package installation

### Required Software

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install -y r-base r-base-dev postgresql postgresql-contrib

# CentOS/RHEL
sudo yum install -y R postgresql postgresql-server

# macOS (with Homebrew)
brew install r postgresql

# Windows
# Download and install R from https://cran.r-project.org/
# Download and install PostgreSQL from https://www.postgresql.org/
```

## ⚙️ Environment Setup

### 1. Database Setup

```sql
-- Create database and user
CREATE DATABASE candidate_scoring;
CREATE USER scoring_read WITH PASSWORD 'secure_read_password';
CREATE USER scoring_write WITH PASSWORD 'secure_write_password';

-- Grant permissions
GRANT CONNECT ON DATABASE candidate_scoring TO scoring_read;
GRANT CONNECT ON DATABASE candidate_scoring TO scoring_write;

-- Grant schema permissions (adjust schema names as needed)
GRANT USAGE ON SCHEMA public TO scoring_read;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO scoring_read;
GRANT USAGE ON SCHEMA public TO scoring_write;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO scoring_write;

-- Set default permissions for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO scoring_read;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO scoring_write;
```

### 2. R Package Installation

```r
# Install required packages
install.packages(c(
  "plumber", "DBI", "RPostgres", "pool", "dplyr", 
  "jsonlite", "logger", "R6", "dotenv", "lubridate",
  "zoo", "testthat", "digest", "stringr"
), dependencies = TRUE)

# Or use the provided script
Rscript scripts/install_packages.R
```

### 3. Environment Configuration

Create environment-specific configuration files:

#### Development (.env.development)
```bash
APP_ENV=development
APP_PORT=5656
APP_HOST=0.0.0.0
LOG_LEVEL=DEBUG

DB_NAME_READ=candidate_scoring_dev
DB_HOST_READ=localhost
DB_PORT_READ=5432
DB_USER_READ=scoring_read
DB_PASSWORD_READ=dev_read_password

DB_NAME_WRITE=candidate_scoring_dev
DB_HOST_WRITE=localhost
DB_PORT_WRITE=5432
DB_USER_WRITE=scoring_write
DB_PASSWORD_WRITE=dev_write_password

VALID_TOKEN=dev_token_12345
```

#### Production (.env.production)
```bash
APP_ENV=production
APP_PORT=5656
APP_HOST=0.0.0.0
LOG_LEVEL=INFO

DB_NAME_READ=candidate_scoring
DB_HOST_READ=prod-db-read.company.com
DB_PORT_READ=5432
DB_USER_READ=scoring_read
DB_PASSWORD_READ=${DB_READ_PASSWORD}

DB_NAME_WRITE=candidate_scoring
DB_HOST_WRITE=prod-db-write.company.com
DB_PORT_WRITE=5432
DB_USER_WRITE=scoring_write
DB_PASSWORD_WRITE=${DB_WRITE_PASSWORD}

VALID_TOKEN=${API_TOKEN}
RATE_LIMIT_PER_MINUTE=1000
```

## 🏠 Local Development

### 1. Clone and Setup

```bash
git clone <repository-url>
cd scoring-candidate

# Install dependencies
Rscript scripts/install_packages.R

# Copy environment file
cp .env.example .env
# Edit .env with your local configuration
```

### 2. Database Setup

```bash
# Start PostgreSQL
sudo systemctl start postgresql

# Create development database
sudo -u postgres createdb candidate_scoring_dev

# Run migrations (if you have them)
# psql -d candidate_scoring_dev -f migrations/schema.sql
```

### 3. Run Tests

```bash
# Run the test suite
Rscript test_refactoring.R

# Run comprehensive tests (if packages are available)
Rscript scripts/run_tests.R
```

### 4. Start Development Server

```bash
# Start the API server
Rscript run.R

# The API will be available at http://localhost:5656
# Swagger documentation at http://localhost:5656/__docs__/
```

## 🎭 Staging Deployment

### 1. Server Preparation

```bash
# Update system
sudo apt-get update && sudo apt-get upgrade -y

# Install required packages
sudo apt-get install -y r-base r-base-dev postgresql-client nginx

# Create application user
sudo useradd -m -s /bin/bash scoring-api
sudo usermod -aG sudo scoring-api
```

### 2. Application Deployment

```bash
# Switch to application user
sudo su - scoring-api

# Clone repository
git clone <repository-url> /home/<USER>/app
cd /home/<USER>/app

# Install R packages
Rscript scripts/install_packages.R

# Set up environment
cp .env.staging .env
# Configure environment variables
```

### 3. Process Management with systemd

Create `/etc/systemd/system/scoring-api.service`:

```ini
[Unit]
Description=Candidate Scoring API
After=network.target postgresql.service

[Service]
Type=simple
User=scoring-api
Group=scoring-api
WorkingDirectory=/home/<USER>/app
ExecStart=/usr/bin/Rscript run.R
Restart=always
RestartSec=10
Environment=PATH=/usr/bin:/usr/local/bin
EnvironmentFile=/home/<USER>/app/.env

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable scoring-api
sudo systemctl start scoring-api

# Check status
sudo systemctl status scoring-api
```

### 4. Reverse Proxy Setup (Nginx)

Create `/etc/nginx/sites-available/scoring-api`:

```nginx
server {
    listen 80;
    server_name staging-api.company.com;

    location / {
        proxy_pass http://localhost:5656;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Health check endpoint
    location /health {
        proxy_pass http://localhost:5656/health;
        access_log off;
    }
}
```

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/scoring-api /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🚀 Production Deployment

### 1. Security Hardening

```bash
# Firewall configuration
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable

# SSL certificate (using Let's Encrypt)
sudo apt-get install certbot python3-certbot-nginx
sudo certbot --nginx -d api.company.com
```

### 2. Production Nginx Configuration

```nginx
server {
    listen 443 ssl http2;
    server_name api.company.com;

    ssl_certificate /etc/letsencrypt/live/api.company.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.company.com/privkey.pem;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;

    location / {
        proxy_pass http://localhost:5656;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Disable Swagger in production
    location /__docs__/ {
        return 404;
    }

    location /openapi.json {
        return 404;
    }
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name api.company.com;
    return 301 https://$server_name$request_uri;
}
```

### 3. Database Configuration

```bash
# Production database connection pooling
# Configure connection limits in PostgreSQL
sudo -u postgres psql -c "ALTER SYSTEM SET max_connections = 200;"
sudo -u postgres psql -c "ALTER SYSTEM SET shared_buffers = '256MB';"
sudo systemctl restart postgresql
```

### 4. Monitoring Setup

```bash
# Log rotation
sudo tee /etc/logrotate.d/scoring-api << EOF
/home/<USER>/app/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    copytruncate
}
EOF

# Health check monitoring (example with cron)
sudo tee /etc/cron.d/scoring-api-health << EOF
*/5 * * * * scoring-api curl -f http://localhost:5656/health || echo "API health check failed" | mail -s "API Alert" <EMAIL>
EOF
```

## 🐳 Docker Deployment

### 1. Build Docker Image

```dockerfile
# Dockerfile is already provided in the repository
docker build -t candidate-scoring-api:latest .
```

### 2. Docker Compose Setup

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  api:
    build: .
    ports:
      - "5656:5656"
    environment:
      - APP_ENV=production
      - DB_HOST_READ=postgres
      - DB_HOST_WRITE=postgres
    env_file:
      - .env.production
    depends_on:
      - postgres
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs

  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: candidate_scoring
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl/certs
    depends_on:
      - api
    restart: unless-stopped

volumes:
  postgres_data:
```

### 3. Deploy with Docker

```bash
# Start services
docker-compose up -d

# Check logs
docker-compose logs -f api

# Scale API instances
docker-compose up -d --scale api=3
```

## 📊 Monitoring & Maintenance

### 1. Health Monitoring

```bash
# API health check
curl -f http://localhost:5656/health

# Database connectivity
curl -f http://localhost:5656/stats

# Log monitoring
tail -f /home/<USER>/app/logs/api.log
```

### 2. Performance Monitoring

```bash
# System resources
htop
iostat -x 1
free -h

# Database performance
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"
```

### 3. Backup Strategy

```bash
# Database backup
pg_dump -h localhost -U postgres candidate_scoring > backup_$(date +%Y%m%d_%H%M%S).sql

# Application backup
tar -czf app_backup_$(date +%Y%m%d_%H%M%S).tar.gz /home/<USER>/app
```

## 🔧 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   ```bash
   # Check PostgreSQL status
   sudo systemctl status postgresql
   
   # Check connection
   psql -h localhost -U scoring_read -d candidate_scoring
   ```

2. **API Not Starting**
   ```bash
   # Check logs
   sudo journalctl -u scoring-api -f
   
   # Check port availability
   sudo netstat -tlnp | grep 5656
   ```

3. **High Memory Usage**
   ```bash
   # Monitor R processes
   ps aux | grep R
   
   # Check database connections
   sudo -u postgres psql -c "SELECT count(*) FROM pg_stat_activity;"
   ```

4. **Performance Issues**
   ```bash
   # Check database performance
   sudo -u postgres psql -c "SELECT query, calls, total_time FROM pg_stat_statements ORDER BY total_time DESC LIMIT 10;"
   
   # Monitor API response times
   curl -w "@curl-format.txt" -o /dev/null -s http://localhost:5656/health
   ```

### Log Analysis

```bash
# Error analysis
grep "ERROR" /home/<USER>/app/logs/api.log | tail -20

# Performance analysis
grep "Request.*completed" /home/<USER>/app/logs/api.log | awk '{print $NF}' | sort -n
```

## 📞 Support

For deployment issues:
1. Check the logs first
2. Verify environment configuration
3. Test database connectivity
4. Contact the development team with specific error messages

## 🔄 Updates and Rollbacks

### Update Procedure

```bash
# Backup current version
cp -r /home/<USER>/app /home/<USER>/app.backup

# Pull updates
cd /home/<USER>/app
git pull origin main

# Test in staging first
# Deploy to production
sudo systemctl restart scoring-api
```

### Rollback Procedure

```bash
# Stop service
sudo systemctl stop scoring-api

# Restore backup
rm -rf /home/<USER>/app
mv /home/<USER>/app.backup /home/<USER>/app

# Start service
sudo systemctl start scoring-api
```
