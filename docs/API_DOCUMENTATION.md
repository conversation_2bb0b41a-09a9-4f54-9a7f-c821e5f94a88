# API Documentation - Candidate Scoring API v2.0.0

This document provides comprehensive documentation for the Candidate Scoring and Matching API endpoints, request/response formats, and usage examples.

## 📋 Table of Contents

- [Base Information](#base-information)
- [Authentication](#authentication)
- [Response Format](#response-format)
- [Error Handling](#error-handling)
- [Endpoints](#endpoints)
- [Examples](#examples)
- [Rate Limiting](#rate-limiting)
- [Changelog](#changelog)

## 🌐 Base Information

- **Base URL**: `https://api.company.com` (production) or `http://localhost:5656` (development)
- **API Version**: 2.0.0
- **Protocol**: HTTPS (production), HTTP (development)
- **Content Type**: `application/json`
- **Character Encoding**: UTF-8

## 🔐 Authentication

All API endpoints (except `/health`) require Bearer token authentication.

### Request Header
```http
Authorization: Bearer <your_access_token>
```

### Example
```bash
curl -H "Authorization: Bearer your_token_here" \
     https://api.company.com/health
```

### Error Response (401 Unauthorized)
```json
{
  "success": false,
  "error": {
    "type": "Unauthorized",
    "message": "Invalid token",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

## 📄 Response Format

All API responses follow a consistent JSON structure:

### Success Response
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    "results": [...],
    "summary": {
      "total_processed": 150,
      "processing_time_seconds": 2.34
    }
  },
  "metadata": {
    "pagination": {
      "current_page": 1,
      "per_page": 50,
      "total_items": 150,
      "total_pages": 3
    }
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_abc123"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "type": "Validation Error",
    "message": "Input validation failed",
    "details": {
      "validation_errors": [
        {
          "field": "job_vacancy_id",
          "message": "Job vacancy ID must be a positive integer"
        }
      ]
    },
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "request_id": "req_abc123"
}
```

## ⚠️ Error Handling

### HTTP Status Codes

| Code | Description | Usage |
|------|-------------|-------|
| 200 | OK | Successful request |
| 400 | Bad Request | Invalid input parameters |
| 401 | Unauthorized | Missing or invalid authentication |
| 404 | Not Found | Resource not found |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | Server-side error |
| 503 | Service Unavailable | System maintenance or overload |

### Error Types

- **Validation Error**: Input parameter validation failed
- **Not Found**: Requested resource doesn't exist
- **Authorization Error**: Authentication or permission issues
- **Internal Server Error**: Unexpected server-side errors
- **Rate Limit Error**: Too many requests in time window

## 🛠️ Endpoints

### 1. Health Check

Check system health and connectivity.

```http
GET /health
```

**Parameters**: None

**Response**:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-15T10:30:00Z",
    "version": "2.0.0",
    "environment": "production",
    "checks": {
      "database": {
        "healthy": true,
        "response_time_ms": 50
      },
      "aws": {
        "healthy": true,
        "response_time_ms": 100
      }
    }
  }
}
```

### 2. Process Candidate Experience

Process candidate experience data and calculate scores.

```http
POST /data_experience_processing/{job_vacancy_id}/{schema}/{recalculate_all}
```

**Parameters**:
- `job_vacancy_id` (integer, required): Job vacancy identifier
- `schema` (string, required): Database schema name
- `recalculate_all` (boolean, required): Whether to recalculate all candidates

**Example Request**:
```bash
curl -X POST \
  -H "Authorization: Bearer your_token" \
  https://api.company.com/data_experience_processing/123/tenant1/false
```

**Response**:
```json
{
  "success": true,
  "message": "Successfully processed 45 candidates",
  "data": {
    "results": [
      {
        "user_job_vacancy_id": 1001,
        "score": 85.5,
        "status": "PASSED",
        "education_score": 4,
        "experience_score": 5,
        "gpa_score": 4,
        "age_score": 5,
        "toefl_score": 3
      }
    ],
    "summary": {
      "total_processed": 45,
      "processing_type": "scoring",
      "score_statistics": {
        "mean_score": 67.8,
        "median_score": 70.0,
        "min_score": 25.5,
        "max_score": 95.0
      },
      "status_distribution": {
        "PASSED": 28,
        "NOT PASSED": 17
      }
    }
  },
  "metadata": {
    "total_candidates": 45,
    "processing_time_seconds": 1.23,
    "configuration_used": {
      "cutoff_value": 40,
      "weights": {
        "education": 20,
        "experience": 25,
        "gpa": 20,
        "age": 15,
        "toefl": 20
      }
    }
  }
}
```

### 3. Candidate Matching

Perform advanced candidate-job matching.

```http
POST /match_making/{job_vacancy_id}/{schema}/{recalculate_all}
```

**Parameters**: Same as candidate experience processing

**Response**:
```json
{
  "success": true,
  "message": "Successfully processed matching for 45 candidates",
  "data": {
    "results": [
      {
        "user_job_vacancy_id": 1001,
        "main_score": 78.5,
        "match_status": "MATCHED",
        "education_match": 85,
        "experience_match": 90,
        "salary_match": 75,
        "location_match": 60,
        "skills_match": 80
      }
    ],
    "summary": {
      "total_processed": 45,
      "processing_type": "matching",
      "matching_statistics": {
        "mean_match_score": 65.2,
        "candidates_above_threshold": 23
      }
    }
  },
  "metadata": {
    "total_candidates": 45,
    "processing_time_seconds": 1.87,
    "baseline_used": {
      "education_level": "S1",
      "minimum_salary": 5000000,
      "maximum_salary": 8000000
    },
    "matching_threshold": 50
  }
}
```

### 4. Get Job Configuration

Retrieve scoring and matching configuration for a job.

```http
GET /job_vacancy/{job_vacancy_id}/{schema}/config
```

**Parameters**:
- `job_vacancy_id` (integer, required): Job vacancy identifier
- `schema` (string, required): Database schema name

**Response**:
```json
{
  "success": true,
  "data": {
    "job_vacancy_id": 123,
    "education_config": {
      "Value": 10,
      "norma_education": [
        {"LastEducation": "S3", "Score": 5},
        {"LastEducation": "S2", "Score": 4},
        {"LastEducation": "S1", "Score": 3}
      ]
    },
    "weights": {
      "education": 20,
      "experience": 25,
      "gpa": 20,
      "age": 15,
      "toefl": 20
    },
    "cutoff_value": 40,
    "matching_threshold": 50,
    "metadata": {
      "retrieved_at": "2024-01-15T10:30:00Z",
      "job_vacancy_id": 123,
      "schema": "tenant1"
    }
  }
}
```

### 5. Get Processing Status

Check the status of a long-running processing operation.

```http
GET /processing_status/{job_id}
```

**Parameters**:
- `job_id` (string, required): Processing job identifier

**Response**:
```json
{
  "success": true,
  "data": {
    "job_id": "job_abc123",
    "status": "completed",
    "progress_percent": 100,
    "started_at": "2024-01-15T10:25:00Z",
    "completed_at": "2024-01-15T10:30:00Z",
    "message": "Processing completed successfully"
  }
}
```

### 6. Get API Statistics

Retrieve API usage statistics and system metrics.

```http
GET /stats
```

**Response**:
```json
{
  "success": true,
  "data": {
    "system": {
      "uptime_seconds": 86400,
      "memory_usage_mb": 256.5,
      "r_version": "R version 4.3.0",
      "platform": "x86_64-pc-linux-gnu"
    },
    "database": {
      "initialized": true,
      "read_pool": {
        "open_connections": 3,
        "max_connections": 5
      },
      "write_pool": {
        "open_connections": 1,
        "max_connections": 3
      }
    },
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

## 📝 Examples

### Complete Workflow Example

```bash
# 1. Check system health
curl -H "Authorization: Bearer your_token" \
     https://api.company.com/health

# 2. Get job configuration
curl -H "Authorization: Bearer your_token" \
     https://api.company.com/job_vacancy/123/tenant1/config

# 3. Process candidate scoring
curl -X POST \
     -H "Authorization: Bearer your_token" \
     https://api.company.com/data_experience_processing/123/tenant1/false

# 4. Perform candidate matching
curl -X POST \
     -H "Authorization: Bearer your_token" \
     https://api.company.com/match_making/123/tenant1/false

# 5. Check API statistics
curl -H "Authorization: Bearer your_token" \
     https://api.company.com/stats
```

### Error Handling Example

```javascript
// JavaScript example with error handling
async function processCandidate(jobId, schema, recalculateAll) {
  try {
    const response = await fetch(
      `https://api.company.com/data_experience_processing/${jobId}/${schema}/${recalculateAll}`,
      {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer your_token',
          'Content-Type': 'application/json'
        }
      }
    );

    const data = await response.json();

    if (!data.success) {
      console.error('API Error:', data.error.message);
      if (data.error.details && data.error.details.validation_errors) {
        data.error.details.validation_errors.forEach(error => {
          console.error(`Validation Error - ${error.field}: ${error.message}`);
        });
      }
      return null;
    }

    return data.data;
  } catch (error) {
    console.error('Network Error:', error.message);
    return null;
  }
}
```

## 🚦 Rate Limiting

The API implements rate limiting to ensure fair usage and system stability.

### Limits
- **Default**: 100 requests per minute per IP address
- **Burst**: Up to 20 additional requests allowed temporarily
- **Headers**: Rate limit information included in response headers

### Response Headers
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248600
```

### Rate Limit Exceeded Response (429)
```json
{
  "success": false,
  "error": {
    "type": "Too Many Requests",
    "message": "Rate limit of 100 requests per minute exceeded",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "request_id": "req_abc123"
}
```

## 📊 Performance Guidelines

### Best Practices
1. **Batch Processing**: Process multiple candidates in single requests when possible
2. **Caching**: Cache job configurations to reduce repeated requests
3. **Incremental Updates**: Use `recalculate_all=false` for better performance
4. **Connection Reuse**: Reuse HTTP connections for multiple requests
5. **Error Handling**: Implement proper retry logic with exponential backoff

### Response Times
- **Health Check**: < 100ms
- **Configuration Retrieval**: < 200ms
- **Candidate Processing**: 1-5 seconds (depending on batch size)
- **Matching**: 2-8 seconds (depending on complexity)

## 🔄 Changelog

### v2.0.0 (Current)
- Complete API refactoring with improved architecture
- Enhanced error handling and validation
- Comprehensive response formatting
- Rate limiting implementation
- Performance optimizations
- Security enhancements

### v1.0.0 (Legacy)
- Basic scoring and matching endpoints
- Limited error handling
- Simple response format

## 📞 Support

For API support:
- **Documentation**: Check this guide and inline API documentation
- **Issues**: Report bugs through the issue tracking system
- **Contact**: Reach out to the development team for technical questions

## 🔒 Security Notes

1. **Token Security**: Keep API tokens secure and rotate them regularly
2. **HTTPS**: Always use HTTPS in production environments
3. **Input Validation**: The API validates all inputs, but clients should also validate
4. **Rate Limiting**: Respect rate limits to avoid service disruption
5. **Error Information**: Error messages are sanitized in production to prevent information disclosure
