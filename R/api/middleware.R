#' API Middleware Components
#' 
#' This module contains reusable middleware functions for the API including
#' authentication, rate limiting, request logging, and error handling.
#' 
#' <AUTHOR> Team
#' @version 2.0.0

library(logger)
library(digest)
library(lubridate)

#' Validate Authorization Header
#' 
#' Validates the Bearer token in the Authorization header
#' 
#' @param auth_header Character string containing the Authorization header
#' @return List with validation result and user context
validate_auth_header <- function(auth_header) {
  tryCatch({
    # Check if header exists
    if (is.null(auth_header) || auth_header == "") {
      return(list(
        valid = FALSE,
        message = "Missing Authorization header",
        user_context = NULL
      ))
    }
    
    # Check Bearer format
    if (!grepl("^Bearer\\s+", auth_header)) {
      return(list(
        valid = FALSE,
        message = "Invalid Authorization header format. Expected 'Bearer <token>'",
        user_context = NULL
      ))
    }
    
    # Extract token
    token <- sub("^Bearer\\s+", "", auth_header)
    if (nchar(token) == 0) {
      return(list(
        valid = FALSE,
        message = "Empty token provided",
        user_context = NULL
      ))
    }
    
    # Validate token
    valid_token <- get_security_config()$valid_token
    if (token != valid_token) {
      return(list(
        valid = FALSE,
        message = "Invalid token",
        user_context = NULL
      ))
    }
    
    # Create user context (in a real system, this would decode JWT or lookup user)
    user_context <- list(
      user_id = "system",
      permissions = c("read", "write"),
      authenticated_at = Sys.time()
    )
    
    return(list(
      valid = TRUE,
      message = "Token validated successfully",
      user_context = user_context
    ))
    
  }, error = function(e) {
    log_error("Error validating auth header: {e$message}")
    return(list(
      valid = FALSE,
      message = "Authentication system error",
      user_context = NULL
    ))
  })
}

#' Request Logging Middleware
#' 
#' Logs incoming requests with timing and response information
#' 
#' @param req Plumber request object
#' @param res Plumber response object
#' @return Middleware function
request_logger <- function(req, res) {
  start_time <- Sys.time()
  request_id <- substr(digest(paste(Sys.time(), runif(1)), algo = "md5"), 1, 8)
  
  # Log request start
  log_info("Request {request_id} started: {req$REQUEST_METHOD} {req$PATH_INFO}")
  
  # Add request ID to request object
  req$request_id <- request_id
  
  # Continue with request processing
  plumber::forward()
  
  # Log request completion (this runs after the endpoint)
  end_time <- Sys.time()
  duration <- as.numeric(difftime(end_time, start_time, units = "secs"))
  
  log_info("Request {request_id} completed: {res$status} in {round(duration, 3)}s")
}

#' Rate Limiting Middleware
#' 
#' Implements simple in-memory rate limiting
#' 
#' @param req Plumber request object
#' @param res Plumber response object
#' @return Middleware function or error response
rate_limiter <- function(req, res) {
  # Simple in-memory rate limiting (in production, use Redis or similar)
  if (!exists(".rate_limit_store", envir = .GlobalEnv)) {
    assign(".rate_limit_store", list(), envir = .GlobalEnv)
  }
  
  # Get client identifier (IP address)
  client_ip <- req$HTTP_X_FORWARDED_FOR %||% req$REMOTE_ADDR %||% "unknown"
  current_time <- Sys.time()
  window_start <- current_time - minutes(1)
  
  # Clean old entries
  store <- get(".rate_limit_store", envir = .GlobalEnv)
  store <- store[sapply(store, function(x) x$timestamp > window_start)]
  
  # Count requests from this client in the current window
  client_requests <- sum(sapply(store, function(x) x$client_ip == client_ip))
  rate_limit <- get_security_config()$rate_limit
  
  if (client_requests >= rate_limit) {
    log_warn("Rate limit exceeded for client {client_ip}: {client_requests} requests")
    res$status <- 429
    res$setHeader("Retry-After", "60")
    return(create_error_response(
      "Too Many Requests",
      paste("Rate limit of", rate_limit, "requests per minute exceeded")
    ))
  }
  
  # Add current request to store
  request_entry <- list(
    client_ip = client_ip,
    timestamp = current_time,
    path = req$PATH_INFO
  )
  
  store[[length(store) + 1]] <- request_entry
  assign(".rate_limit_store", store, envir = .GlobalEnv)
  
  plumber::forward()
}

#' Error Handling Middleware
#' 
#' Global error handler for unhandled exceptions
#' 
#' @param req Plumber request object
#' @param res Plumber response object
#' @param err Error object
#' @return Error response
error_handler <- function(req, res, err) {
  request_id <- req$request_id %||% "unknown"
  
  # Log the error
  log_error("Request {request_id} error: {err$message}")
  
  # Don't expose internal errors in production
  if (get_app_config()$is_production()) {
    error_message <- "An internal server error occurred"
  } else {
    error_message <- err$message
  }
  
  # Set appropriate status code
  if (inherits(err, "validation_error")) {
    res$status <- 400
  } else if (inherits(err, "not_found_error")) {
    res$status <- 404
  } else {
    res$status <- 500
  }
  
  create_error_response("Internal Server Error", error_message, request_id)
}

#' Input Sanitization Middleware
#' 
#' Sanitizes and validates common input parameters
#' 
#' @param req Plumber request object
#' @param res Plumber response object
#' @return Middleware function or error response
input_sanitizer <- function(req, res) {
  tryCatch({
    # Sanitize path parameters
    if (!is.null(req$args$job_vacancy_id)) {
      job_vacancy_id <- as.integer(req$args$job_vacancy_id)
      if (is.na(job_vacancy_id) || job_vacancy_id <= 0) {
        res$status <- 400
        return(create_error_response("Bad Request", "Invalid job_vacancy_id: must be a positive integer"))
      }
      req$args$job_vacancy_id <- job_vacancy_id
    }
    
    if (!is.null(req$args$schema)) {
      schema <- trimws(req$args$schema)
      if (nchar(schema) == 0 || !grepl("^[a-zA-Z0-9_]+$", schema)) {
        res$status <- 400
        return(create_error_response("Bad Request", "Invalid schema: must contain only alphanumeric characters and underscores"))
      }
      req$args$schema <- schema
    }
    
    if (!is.null(req$args$recalculate_all)) {
      recalculate_all <- as.logical(req$args$recalculate_all)
      if (is.na(recalculate_all)) {
        res$status <- 400
        return(create_error_response("Bad Request", "Invalid recalculate_all: must be true or false"))
      }
      req$args$recalculate_all <- recalculate_all
    }
    
    plumber::forward()
    
  }, error = function(e) {
    log_error("Input sanitization error: {e$message}")
    res$status <- 400
    return(create_error_response("Bad Request", "Invalid input parameters"))
  })
}

#' Health Check Middleware
#' 
#' Provides system health status
#' 
#' @param req Plumber request object
#' @param res Plumber response object
#' @return Health status response
health_check <- function(req, res) {
  tryCatch({
    # Check database connectivity
    db_status <- check_database_health()
    
    # Check AWS connectivity
    aws_status <- check_aws_health()
    
    # Overall health
    overall_healthy <- db_status$healthy && aws_status$healthy
    
    if (!overall_healthy) {
      res$status <- 503
    }
    
    list(
      status = if (overall_healthy) "healthy" else "unhealthy",
      timestamp = Sys.time(),
      version = get_app_config()$get(c("app", "version")),
      environment = get_app_config()$get(c("app", "environment")),
      checks = list(
        database = db_status,
        aws = aws_status
      )
    )
    
  }, error = function(e) {
    log_error("Health check error: {e$message}")
    res$status <- 503
    list(
      status = "unhealthy",
      timestamp = Sys.time(),
      error = "Health check failed"
    )
  })
}

#' Check Database Health
#' 
#' @return List with database health status
check_database_health <- function() {
  tryCatch({
    # This would be implemented with actual database connection test
    list(
      healthy = TRUE,
      message = "Database connection successful",
      response_time_ms = 50
    )
  }, error = function(e) {
    list(
      healthy = FALSE,
      message = paste("Database connection failed:", e$message),
      response_time_ms = NA
    )
  })
}

#' Check AWS Health
#' 
#' @return List with AWS health status
check_aws_health <- function() {
  tryCatch({
    # This would be implemented with actual AWS service test
    list(
      healthy = TRUE,
      message = "AWS services accessible",
      response_time_ms = 100
    )
  }, error = function(e) {
    list(
      healthy = FALSE,
      message = paste("AWS services unavailable:", e$message),
      response_time_ms = NA
    )
  })
}
