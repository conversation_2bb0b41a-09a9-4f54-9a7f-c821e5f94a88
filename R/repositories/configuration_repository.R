#' Configuration Repository
#' 
#' This repository handles all database operations related to job configurations
#' including scoring rules, matching criteria, and baseline requirements.
#' 
#' <AUTHOR> Team
#' @version 2.0.0

library(logger)
library(jsonlite)
library(dplyr)
library(R6)

# Source required dependencies
source("R/repositories/database_manager.R")

#' Configuration Repository Class
#' 
#' Manages configuration data access operations
ConfigurationRepository <- R6::R6Class(
  "ConfigurationRepository",
  
  private = list(
    .db_manager = NULL,
    
    parse_json_config = function(json_string) {
      if (is.null(json_string) || nchar(trimws(json_string)) == 0) {
        return(NULL)
      }
      
      tryCatch({
        # Clean JSON string for Python-style formatting
        clean_json <- json_string %>%
          stringr::str_replace_all("u'", "\"") %>%
          stringr::str_replace_all("'", "\"") %>%
          stringr::str_replace_all("\n", "") %>%
          stringr::str_replace_all(",\\s*\\}", "}") %>%
          stringr::str_replace_all(",\\s*\\]", "]")
        
        # Parse JSON
        config <- fromJSON(clean_json, simplifyVector = FALSE)
        return(config)
        
      }, error = function(e) {
        log_error("Failed to parse JSON configuration: {e$message}")
        return(NULL)
      })
    },
    
    build_default_config = function() {
      list(
        Education = list(
          Value = 10,
          norma_education = data.frame(
            LastEducation = c("S3", "S2", "S1", "D4", "D3"),
            Score = c(5, 4, 3, 2, 1)
          )
        ),
        Age = list(
          Value = 10,
          norma_age = data.frame(
            Minimum_Age = c(18, 30, 33, 36),
            Maximum_Age = c(30, 33, 36, 55),
            Score = c(5, 4, 3, 2)
          )
        ),
        Working_Experience = list(
          Value = 10,
          norma_we = data.frame(
            Min_Exp = c(3, 2, 1, 0),
            Max_Exp = c(30, 3, 2, 1),
            Score = c(5, 4, 3, 2)
          )
        ),
        GPA = list(
          Value = 10,
          norma_gpa = data.frame(
            Minimum = c(3.5, 3.2, 2.9, 2.7),
            Maximum = c(4.0, 3.5, 3.2, 2.9),
            Score = c(5, 4, 3, 2)
          )
        ),
        Campus = list(
          Value = 10,
          norma_campus = data.frame(
            Description = c("Top Tier", "Mid Tier", "Lower Tier"),
            Score = c(3, 2, 1)
          )
        ),
        CutoffStatus = list(
          Value = 40.0
        ),
        weights = list(
          education = 20,
          age = 15,
          experience = 25,
          gpa = 20,
          campus = 20
        )
      )
    }
  ),
  
  public = list(
    #' Initialize the repository
    initialize = function() {
      private$.db_manager <- get_db_manager()
      log_debug("Configuration repository initialized")
    },
    
    #' Get job configuration
    #' @param job_vacancy_id Job vacancy identifier
    #' @param schema Database schema
    #' @return Configuration object or NULL if not found
    get_job_configuration = function(job_vacancy_id, schema) {
      tryCatch({
        # Set schema
        private$.db_manager$set_schema(schema, "read")
        
        # Get configuration from database
        config_query <- "
          SELECT 
            jv.id,
            jv.name,
            jv.config->>'matchmaking_config' as matchmaking_config
          FROM job_vacancies jv
          WHERE jv.id = ?job_vacancy_id
            AND jv.discarded_at IS NULL
        "
        
        result <- private$.db_manager$execute_query(
          config_query,
          list(job_vacancy_id = job_vacancy_id),
          "read"
        )
        
        if (nrow(result) == 0) {
          log_warn("Job vacancy {job_vacancy_id} not found in schema {schema}")
          return(NULL)
        }
        
        # Parse configuration
        config_json <- result$matchmaking_config[1]
        parsed_config <- private$parse_json_config(config_json)
        
        if (is.null(parsed_config)) {
          log_warn("Using default configuration for job {job_vacancy_id}")
          parsed_config <- private$build_default_config()
        }
        
        # Add job metadata
        parsed_config$job_info <- list(
          job_vacancy_id = job_vacancy_id,
          job_name = result$name[1],
          schema = schema
        )
        
        return(parsed_config)
        
      }, error = function(e) {
        log_error("Error retrieving job configuration: {e$message}")
        stop(paste("Failed to retrieve job configuration:", e$message))
      })
    },
    
    #' Get job baseline requirements
    #' @param job_vacancy_id Job vacancy identifier
    #' @param schema Database schema
    #' @return Baseline requirements or NULL if not found
    get_job_baseline = function(job_vacancy_id, schema) {
      tryCatch({
        # Set schema
        private$.db_manager$set_schema(schema, "read")
        
        # Read baseline query from file
        baseline_query_file <- "queries/base_line.sql"
        if (!file.exists(baseline_query_file)) {
          log_error("Baseline query file not found: {baseline_query_file}")
          return(NULL)
        }
        
        baseline_query <- paste(readLines(baseline_query_file), collapse = "\n")
        
        result <- private$.db_manager$execute_query(
          baseline_query,
          list(job_vacancy_id = job_vacancy_id),
          "read"
        )
        
        if (nrow(result) == 0) {
          log_warn("No baseline data found for job {job_vacancy_id}")
          return(NULL)
        }
        
        # Process baseline data
        baseline <- list(
          education_level = result$`Education Level`[1],
          job_level = result$job_level[1],
          minimum_salary = as.numeric(result$minimum_salary[1]),
          maximum_salary = as.numeric(result$maximum_salary[1]),
          domicile = result$Domicile[1],
          previous_job_industry = result$`Previous Job Industry`[1],
          tools_and_competencies = result$`Tools and Competencies Mastery`[1]
        )
        
        return(baseline)
        
      }, error = function(e) {
        log_error("Error retrieving job baseline: {e$message}")
        stop(paste("Failed to retrieve job baseline:", e$message))
      })
    },
    
    #' Update job configuration
    #' @param job_vacancy_id Job vacancy identifier
    #' @param schema Database schema
    #' @param config Configuration object
    #' @return Boolean indicating success
    update_job_configuration = function(job_vacancy_id, schema, config) {
      tryCatch({
        # Validate configuration
        if (is.null(config) || !is.list(config)) {
          stop("Invalid configuration object")
        }
        
        # Set schema
        private$.db_manager$set_schema(schema, "write")
        
        # Convert configuration to JSON
        config_json <- toJSON(config, auto_unbox = TRUE, pretty = TRUE)
        
        # Update configuration in database
        update_query <- "
          UPDATE job_vacancies 
          SET config = jsonb_set(
            COALESCE(config, '{}'::jsonb), 
            '{matchmaking_config}', 
            ?config_json::jsonb
          ),
          updated_at = NOW()
          WHERE id = ?job_vacancy_id
        "
        
        rows_affected <- private$.db_manager$execute_statement(
          update_query,
          list(
            config_json = config_json,
            job_vacancy_id = job_vacancy_id
          )
        )
        
        if (rows_affected == 0) {
          log_warn("No job vacancy found with ID {job_vacancy_id}")
          return(FALSE)
        }
        
        log_info("Updated configuration for job {job_vacancy_id}")
        return(TRUE)
        
      }, error = function(e) {
        log_error("Error updating job configuration: {e$message}")
        stop(paste("Failed to update job configuration:", e$message))
      })
    },
    
    #' Get valid schemas
    #' @return List of valid schema names
    get_valid_schemas = function() {
      tryCatch({
        schema_query_file <- "queries/schema_list_production.sql"
        if (!file.exists(schema_query_file)) {
          log_warn("Schema query file not found, using default schemas")
          return(c("public", "development", "staging", "production"))
        }
        
        schema_query <- paste(readLines(schema_query_file), collapse = "\n")
        
        result <- private$.db_manager$execute_query(schema_query, list(), "read")
        
        if (nrow(result) == 0) {
          log_warn("No schemas found in database")
          return(c("public"))
        }
        
        schemas <- result$scheme
        log_info("Found {length(schemas)} valid schemas")
        return(schemas)
        
      }, error = function(e) {
        log_error("Error retrieving valid schemas: {e$message}")
        return(c("public"))  # Return default schema on error
      })
    },
    
    #' Validate schema exists
    #' @param schema Schema name to validate
    #' @return Boolean indicating if schema is valid
    validate_schema_exists = function(schema) {
      tryCatch({
        valid_schemas <- self$get_valid_schemas()
        is_valid <- schema %in% valid_schemas
        
        if (!is_valid) {
          log_warn("Schema '{schema}' is not in the list of valid schemas")
        }
        
        return(is_valid)
        
      }, error = function(e) {
        log_error("Error validating schema: {e$message}")
        return(FALSE)
      })
    },
    
    #' Get configuration template
    #' @param template_type Type of configuration template
    #' @return Configuration template
    get_configuration_template = function(template_type = "default") {
      tryCatch({
        if (template_type == "default") {
          return(private$build_default_config())
        }
        
        # Could add other template types here
        log_warn("Unknown template type: {template_type}, returning default")
        return(private$build_default_config())
        
      }, error = function(e) {
        log_error("Error getting configuration template: {e$message}")
        return(private$build_default_config())
      })
    },
    
    #' Get configuration history
    #' @param job_vacancy_id Job vacancy identifier
    #' @param schema Database schema
    #' @param limit Number of history records to retrieve
    #' @return Configuration history
    get_configuration_history = function(job_vacancy_id, schema, limit = 10) {
      tryCatch({
        # This would require an audit table in a real implementation
        # For now, return empty history
        log_info("Configuration history not implemented yet")
        return(list())
        
      }, error = function(e) {
        log_error("Error retrieving configuration history: {e$message}")
        return(list())
      })
    }
  )
)
