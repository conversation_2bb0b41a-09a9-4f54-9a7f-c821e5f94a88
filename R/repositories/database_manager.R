#' Database Connection Management
#' 
#' This module provides a robust database connection management system with
#' connection pooling, automatic retry, and proper resource cleanup.
#' 
#' <AUTHOR> Team
#' @version 2.0.0

library(DBI)
library(RPostgres)
library(pool)
library(logger)
library(R6)

#' Database Manager Class
#' 
#' Manages database connections with pooling and error handling
DatabaseManager <- R6::R6Class(
  "DatabaseManager",
  
  private = list(
    .read_pool = NULL,
    .write_pool = NULL,
    .initialized = FALSE,
    
    create_connection_pool = function(config, pool_name) {
      tryCatch({
        pool <- dbPool(
          drv = RPostgres::Postgres(),
          host = config$host,
          port = config$port,
          dbname = config$dbname,
          user = config$user,
          password = config$password,
          minSize = 1,
          maxSize = config$pool_size,
          idleTimeout = config$timeout * 1000  # Convert to milliseconds
        )
        
        log_info("Created {pool_name} connection pool with max size {config$pool_size}")
        return(pool)
        
      }, error = function(e) {
        log_error("Failed to create {pool_name} connection pool: {e$message}")
        stop(paste("Database connection failed:", e$message))
      })
    }
  ),
  
  public = list(
    #' Initialize database manager
    initialize = function() {
      if (private$.initialized) {
        log_warn("Database manager already initialized")
        return(invisible(self))
      }
      
      # Get database configurations
      read_config <- get_db_config("read")
      write_config <- get_db_config("write")
      
      # Create connection pools
      private$.read_pool <- private$create_connection_pool(read_config, "read")
      private$.write_pool <- private$create_connection_pool(write_config, "write")
      
      private$.initialized <- TRUE
      log_info("Database manager initialized successfully")
      
      invisible(self)
    },
    
    #' Get read connection
    #' @return Database connection for read operations
    get_read_connection = function() {
      if (!private$.initialized) {
        stop("Database manager not initialized")
      }
      
      tryCatch({
        poolCheckout(private$.read_pool)
      }, error = function(e) {
        log_error("Failed to get read connection: {e$message}")
        stop("Database read connection unavailable")
      })
    },
    
    #' Get write connection
    #' @return Database connection for write operations
    get_write_connection = function() {
      if (!private$.initialized) {
        stop("Database manager not initialized")
      }
      
      tryCatch({
        poolCheckout(private$.write_pool)
      }, error = function(e) {
        log_error("Failed to get write connection: {e$message}")
        stop("Database write connection unavailable")
      })
    },
    
    #' Return connection to pool
    #' @param conn Database connection to return
    return_connection = function(conn) {
      tryCatch({
        poolReturn(conn)
      }, error = function(e) {
        log_warn("Failed to return connection to pool: {e$message}")
      })
    },
    
    #' Execute query with automatic connection management
    #' @param query SQL query string
    #' @param params Query parameters
    #' @param connection_type Either "read" or "write"
    #' @return Query results
    execute_query = function(query, params = list(), connection_type = "read") {
      if (!connection_type %in% c("read", "write")) {
        stop("Connection type must be 'read' or 'write'")
      }
      
      conn <- if (connection_type == "read") {
        self$get_read_connection()
      } else {
        self$get_write_connection()
      }
      
      tryCatch({
        # Set work memory for better performance
        if (connection_type == "read") {
          dbExecute(conn, "SET work_mem='32MB'")
        }
        
        # Execute query with parameters
        if (length(params) > 0) {
          safe_query <- sqlInterpolate(conn, query, .dots = params)
          result <- dbGetQuery(conn, safe_query)
        } else {
          result <- dbGetQuery(conn, query)
        }
        
        log_debug("Executed {connection_type} query successfully")
        return(result)
        
      }, error = function(e) {
        log_error("Query execution failed: {e$message}")
        stop(paste("Database query failed:", e$message))
      }, finally = {
        self$return_connection(conn)
      })
    },
    
    #' Execute statement (INSERT, UPDATE, DELETE)
    #' @param statement SQL statement
    #' @param params Statement parameters
    #' @return Number of affected rows
    execute_statement = function(statement, params = list()) {
      conn <- self$get_write_connection()
      
      tryCatch({
        if (length(params) > 0) {
          safe_statement <- sqlInterpolate(conn, statement, .dots = params)
          result <- dbExecute(conn, safe_statement)
        } else {
          result <- dbExecute(conn, statement)
        }
        
        log_debug("Executed statement successfully, {result} rows affected")
        return(result)
        
      }, error = function(e) {
        log_error("Statement execution failed: {e$message}")
        stop(paste("Database statement failed:", e$message))
      }, finally = {
        self$return_connection(conn)
      })
    },
    
    #' Execute transaction
    #' @param transaction_func Function containing transaction logic
    #' @return Transaction result
    execute_transaction = function(transaction_func) {
      conn <- self$get_write_connection()
      
      tryCatch({
        dbBegin(conn)
        
        result <- transaction_func(conn)
        
        dbCommit(conn)
        log_debug("Transaction committed successfully")
        return(result)
        
      }, error = function(e) {
        dbRollback(conn)
        log_error("Transaction rolled back due to error: {e$message}")
        stop(paste("Transaction failed:", e$message))
      }, finally = {
        self$return_connection(conn)
      })
    },
    
    #' Set schema for connection
    #' @param schema Schema name
    #' @param connection_type Either "read" or "write"
    set_schema = function(schema, connection_type = "read") {
      # Validate schema name
      validation_result <- validate_schema(schema)
      if (!validation_result$valid) {
        stop(paste("Invalid schema:", paste(validation_result$get_error_messages(), collapse = ", ")))
      }
      
      conn <- if (connection_type == "read") {
        self$get_read_connection()
      } else {
        self$get_write_connection()
      }
      
      tryCatch({
        schema_sql <- "SET search_path = ?schema"
        safe_query <- sqlInterpolate(conn, schema_sql, schema = schema)
        dbExecute(conn, safe_query)
        
        log_debug("Set schema to '{schema}' for {connection_type} connection")
        
      }, error = function(e) {
        log_error("Failed to set schema: {e$message}")
        stop(paste("Failed to set schema:", e$message))
      }, finally = {
        self$return_connection(conn)
      })
    },
    
    #' Check database health
    #' @return List with health status
    check_health = function() {
      tryCatch({
        start_time <- Sys.time()
        
        # Test read connection
        read_result <- self$execute_query("SELECT 1 as test", connection_type = "read")
        read_healthy <- nrow(read_result) == 1 && read_result$test == 1
        
        # Test write connection
        write_conn <- self$get_write_connection()
        write_result <- dbGetQuery(write_conn, "SELECT 1 as test")
        write_healthy <- nrow(write_result) == 1 && write_result$test == 1
        self$return_connection(write_conn)
        
        end_time <- Sys.time()
        response_time <- as.numeric(difftime(end_time, start_time, units = "secs")) * 1000
        
        overall_healthy <- read_healthy && write_healthy
        
        return(list(
          healthy = overall_healthy,
          response_time_ms = round(response_time, 2),
          read_pool_healthy = read_healthy,
          write_pool_healthy = write_healthy,
          read_pool_size = poolDbInfo(private$.read_pool)$dbOpenConnections,
          write_pool_size = poolDbInfo(private$.write_pool)$dbOpenConnections
        ))
        
      }, error = function(e) {
        log_error("Database health check failed: {e$message}")
        return(list(
          healthy = FALSE,
          error = e$message,
          response_time_ms = NA
        ))
      })
    },
    
    #' Get connection pool statistics
    #' @return List with pool statistics
    get_pool_stats = function() {
      if (!private$.initialized) {
        return(list(initialized = FALSE))
      }
      
      tryCatch({
        read_info <- poolDbInfo(private$.read_pool)
        write_info <- poolDbInfo(private$.write_pool)
        
        return(list(
          initialized = TRUE,
          read_pool = list(
            open_connections = read_info$dbOpenConnections,
            max_connections = read_info$maxSize,
            idle_connections = read_info$idleConnections
          ),
          write_pool = list(
            open_connections = write_info$dbOpenConnections,
            max_connections = write_info$maxSize,
            idle_connections = write_info$idleConnections
          )
        ))
        
      }, error = function(e) {
        log_error("Failed to get pool statistics: {e$message}")
        return(list(
          initialized = TRUE,
          error = e$message
        ))
      })
    },
    
    #' Cleanup and close connections
    finalize = function() {
      if (private$.initialized) {
        tryCatch({
          if (!is.null(private$.read_pool)) {
            poolClose(private$.read_pool)
            log_info("Closed read connection pool")
          }
          
          if (!is.null(private$.write_pool)) {
            poolClose(private$.write_pool)
            log_info("Closed write connection pool")
          }
          
          private$.initialized <- FALSE
          log_info("Database manager cleanup completed")
          
        }, error = function(e) {
          log_error("Error during database cleanup: {e$message}")
        })
      }
    }
  )
)

# Create global database manager instance
db_manager <- DatabaseManager$new()

#' Get database manager instance
#' @return DatabaseManager instance
get_db_manager <- function() {
  if (!db_manager$.__enclos_env__$private$.initialized) {
    db_manager$initialize()
  }
  db_manager
}

#' Execute database query with automatic connection management
#' @param query SQL query string
#' @param params Query parameters
#' @param connection_type Either "read" or "write"
#' @return Query results
execute_db_query <- function(query, params = list(), connection_type = "read") {
  get_db_manager()$execute_query(query, params, connection_type)
}

#' Execute database statement with automatic connection management
#' @param statement SQL statement
#' @param params Statement parameters
#' @return Number of affected rows
execute_db_statement <- function(statement, params = list()) {
  get_db_manager()$execute_statement(statement, params)
}
