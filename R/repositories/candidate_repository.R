#' Candidate Repository
#' 
#' This repository handles all database operations related to candidates
#' including data retrieval, scoring result persistence, and statistics.
#' 
#' <AUTHOR> Team
#' @version 2.0.0

library(logger)
library(dplyr)
library(R6)

# Source required dependencies
source("R/repositories/database_manager.R")

#' Candidate Repository Class
#' 
#' Manages candidate data access operations
CandidateRepository <- R6::R6Class(
  "CandidateRepository",
  
  private = list(
    .db_manager = NULL,
    
    build_candidate_query = function(job_vacancy_id, recalculate_all = FALSE) {
      base_query <- "
        SELECT 
          cd.user_job_vacancy_id,
          cd.fullname,
          cd.degree,
          cd.major,
          cd.institution,
          cd.gpa,
          cd.dob,
          cd.kota_kab,
          cd.expect_min,
          cd.calculated_yoe as experience,
          cd.skill_tools,
          cd.industry
        FROM candidate_datas cd
      "
      
      if (!recalculate_all) {
        base_query <- paste(base_query, "
          LEFT JOIN user_vacancy_matchmaking_results uvmr
            ON cd.user_job_vacancy_id = uvmr.user_job_vacancy_id
        ")
      }
      
      base_query <- paste(base_query, "
        WHERE cd.job_vacancy_id = ?job_vacancy_id
      ")
      
      if (!recalculate_all) {
        base_query <- paste(base_query, "
          AND uvmr.score IS NULL
        ")
      }
      
      return(base_query)
    },
    
    build_experience_query = function(job_vacancy_id, recalculate_all = FALSE) {
      base_query <- "
        SELECT 
          ujv.id as user_job_vacancy_id,
          ujv.user_id,
          e.job_role,
          e.industry,
          e.starts_at,
          e.ends_at,
          e.work_type
        FROM user_job_vacancies ujv
        JOIN experiences e ON e.user_id = ujv.user_id
      "
      
      if (!recalculate_all) {
        base_query <- paste(base_query, "
          LEFT JOIN user_vacancy_matchmaking_results uvmr
            ON ujv.id = uvmr.user_job_vacancy_id
        ")
      }
      
      base_query <- paste(base_query, "
        WHERE ujv.discarded_at IS NULL
          AND ujv.state != 'pool'
          AND e.work_type != 'intern'
          AND ujv.job_vacancy_id = ?job_vacancy_id
      ")
      
      if (!recalculate_all) {
        base_query <- paste(base_query, "
          AND uvmr.score IS NULL
        ")
      }
      
      return(base_query)
    }
  ),
  
  public = list(
    #' Initialize the repository
    initialize = function() {
      private$.db_manager <- get_db_manager()
      log_debug("Candidate repository initialized")
    },
    
    #' Get candidates for scoring
    #' @param job_vacancy_id Job vacancy identifier
    #' @param schema Database schema
    #' @param recalculate_all Whether to include already processed candidates
    #' @return Data frame with candidate information
    get_candidates_for_scoring = function(job_vacancy_id, schema, recalculate_all = FALSE) {
      tryCatch({
        # Set schema
        private$.db_manager$set_schema(schema, "read")
        
        # Build and execute candidate query
        candidate_query <- private$build_candidate_query(job_vacancy_id, recalculate_all)
        candidates <- private$.db_manager$execute_query(
          candidate_query,
          list(job_vacancy_id = job_vacancy_id),
          "read"
        )
        
        if (nrow(candidates) == 0) {
          log_info("No candidates found for job {job_vacancy_id}")
          return(data.frame())
        }
        
        # Get experience data
        experience_query <- private$build_experience_query(job_vacancy_id, recalculate_all)
        experience_data <- private$.db_manager$execute_query(
          experience_query,
          list(job_vacancy_id = job_vacancy_id),
          "read"
        )
        
        # Process and join data
        processed_candidates <- self$process_candidate_data(candidates, experience_data)
        
        log_info("Retrieved {nrow(processed_candidates)} candidates for scoring")
        return(processed_candidates)
        
      }, error = function(e) {
        log_error("Error retrieving candidates: {e$message}")
        stop(paste("Failed to retrieve candidates:", e$message))
      })
    },
    
    #' Process and enrich candidate data
    #' @param candidates Raw candidate data
    #' @param experience_data Raw experience data
    #' @return Processed candidate data frame
    process_candidate_data = function(candidates, experience_data) {
      tryCatch({
        # Calculate age from date of birth
        candidates <- candidates %>%
          mutate(
            dob = as.Date(dob),
            age = as.numeric(difftime(Sys.Date(), dob, units = "days")) / 365.25,
            age = floor(age)
          )
        
        # Process experience data if available
        if (nrow(experience_data) > 0) {
          # Calculate years of experience
          current_date <- Sys.Date()
          
          experience_summary <- experience_data %>%
            mutate(
              starts_at = as.Date(starts_at),
              ends_at = coalesce(as.Date(ends_at), current_date),
              experience_years = as.numeric(difftime(ends_at, starts_at, units = "days")) / 365.25
            ) %>%
            group_by(user_job_vacancy_id) %>%
            summarise(
              total_experience = sum(experience_years, na.rm = TRUE),
              industries = paste(unique(na.omit(industry)), collapse = ", "),
              .groups = "drop"
            )
          
          # Join with candidates
          candidates <- candidates %>%
            left_join(experience_summary, by = "user_job_vacancy_id") %>%
            mutate(
              experience = coalesce(total_experience, experience, 0),
              industry = coalesce(industries, industry, "")
            ) %>%
            select(-total_experience, -industries)
        }
        
        # Clean and standardize data
        candidates <- candidates %>%
          mutate(
            fullname = trimws(fullname),
            degree = trimws(toupper(degree)),
            major = trimws(major),
            institution = trimws(institution),
            gpa = as.numeric(gpa),
            expect_min = as.numeric(expect_min),
            experience = as.numeric(experience)
          ) %>%
          # Rename columns to match expected format
          rename(
            Candidate = fullname,
            Education = degree,
            Major = major,
            Institution = institution,
            GPA = gpa,
            Domisili = kota_kab,
            Expected_Salary = expect_min,
            Experience = experience,
            Industry = industry,
            Skillntools = skill_tools
          )
        
        return(candidates)
        
      }, error = function(e) {
        log_error("Error processing candidate data: {e$message}")
        stop(paste("Failed to process candidate data:", e$message))
      })
    },
    
    #' Save scoring results to database
    #' @param results Data frame with scoring results
    #' @param schema Database schema
    #' @return Number of rows affected
    save_scoring_results = function(results, schema) {
      tryCatch({
        if (nrow(results) == 0) {
          log_info("No results to save")
          return(0)
        }
        
        # Set schema for write operations
        private$.db_manager$set_schema(schema, "write")
        
        # Use transaction for data integrity
        rows_affected <- private$.db_manager$execute_transaction(function(conn) {
          total_rows <- 0
          
          # Process in batches to avoid memory issues
          batch_size <- 1000
          num_batches <- ceiling(nrow(results) / batch_size)
          
          for (i in 1:num_batches) {
            start_idx <- (i - 1) * batch_size + 1
            end_idx <- min(i * batch_size, nrow(results))
            batch_data <- results[start_idx:end_idx, ]
            
            # Build batch insert query
            values_list <- apply(batch_data, 1, function(row) {
              paste0(
                "(", row["user_job_vacancy_id"], ", ",
                row["score"], ", ",
                "'", row["status"], "', ",
                "NOW(), NOW())"
              )
            })
            
            insert_query <- paste0(
              "INSERT INTO user_vacancy_matchmaking_results ",
              "(user_job_vacancy_id, score, status, created_at, updated_at) VALUES ",
              paste(values_list, collapse = ", "),
              " ON CONFLICT (user_job_vacancy_id) DO UPDATE SET ",
              "score = EXCLUDED.score, ",
              "status = EXCLUDED.status, ",
              "updated_at = EXCLUDED.updated_at"
            )
            
            batch_rows <- dbExecute(conn, insert_query)
            total_rows <- total_rows + batch_rows
            
            log_debug("Saved batch {i}/{num_batches}: {batch_rows} rows")
          }
          
          return(total_rows)
        })
        
        log_info("Successfully saved {rows_affected} scoring results")
        return(rows_affected)
        
      }, error = function(e) {
        log_error("Error saving scoring results: {e$message}")
        stop(paste("Failed to save scoring results:", e$message))
      })
    },
    
    #' Get scoring statistics for a job
    #' @param job_vacancy_id Job vacancy identifier
    #' @param schema Database schema
    #' @return Statistics data frame
    get_scoring_statistics = function(job_vacancy_id, schema) {
      tryCatch({
        # Set schema
        private$.db_manager$set_schema(schema, "read")
        
        stats_query <- "
          SELECT 
            COUNT(*) as total_candidates,
            COUNT(CASE WHEN uvmr.status = 'PASSED' THEN 1 END) as passed_candidates,
            COUNT(CASE WHEN uvmr.status = 'NOT PASSED' THEN 1 END) as failed_candidates,
            AVG(uvmr.score) as average_score,
            MIN(uvmr.score) as min_score,
            MAX(uvmr.score) as max_score,
            STDDEV(uvmr.score) as score_stddev
          FROM candidate_datas cd
          LEFT JOIN user_vacancy_matchmaking_results uvmr 
            ON cd.user_job_vacancy_id = uvmr.user_job_vacancy_id
          WHERE cd.job_vacancy_id = ?job_vacancy_id
        "
        
        stats <- private$.db_manager$execute_query(
          stats_query,
          list(job_vacancy_id = job_vacancy_id),
          "read"
        )
        
        # Convert to proper data types
        if (nrow(stats) > 0) {
          stats <- stats %>%
            mutate(
              total_candidates = as.integer(total_candidates),
              passed_candidates = as.integer(coalesce(passed_candidates, 0)),
              failed_candidates = as.integer(coalesce(failed_candidates, 0)),
              average_score = round(as.numeric(coalesce(average_score, 0)), 2),
              min_score = round(as.numeric(coalesce(min_score, 0)), 2),
              max_score = round(as.numeric(coalesce(max_score, 0)), 2),
              score_stddev = round(as.numeric(coalesce(score_stddev, 0)), 2)
            )
        }
        
        return(stats)
        
      }, error = function(e) {
        log_error("Error retrieving scoring statistics: {e$message}")
        stop(paste("Failed to retrieve scoring statistics:", e$message))
      })
    },
    
    #' Get TOEFL scores for candidates
    #' @param job_vacancy_id Job vacancy identifier
    #' @param schema Database schema
    #' @param recalculate_all Whether to include already processed candidates
    #' @return TOEFL scores data frame
    get_toefl_scores = function(job_vacancy_id, schema, recalculate_all = FALSE) {
      tryCatch({
        # Set schema
        private$.db_manager$set_schema(schema, "read")
        
        # Read TOEFL query from file
        toefl_query_file <- "queries/toefl_scores.sql"
        if (!file.exists(toefl_query_file)) {
          log_warn("TOEFL query file not found: {toefl_query_file}")
          return(data.frame())
        }
        
        toefl_query <- paste(readLines(toefl_query_file), collapse = "\n")
        
        # Add filtering conditions
        if (!recalculate_all) {
          toefl_query <- paste(toefl_query, "
            LEFT JOIN user_vacancy_matchmaking_results uvmr
              ON btd.user_job_vacancy_id = uvmr.user_job_vacancy_id
          ")
        }
        
        toefl_query <- paste(toefl_query, "
          WHERE btd.job_vacancy_id = ?job_vacancy_id
        ")
        
        if (!recalculate_all) {
          toefl_query <- paste(toefl_query, "
            AND uvmr.main_score IS NULL
          ")
        }
        
        toefl_data <- private$.db_manager$execute_query(
          toefl_query,
          list(job_vacancy_id = job_vacancy_id),
          "read"
        )
        
        # Convert TOEFL scores to numeric
        if (nrow(toefl_data) > 0 && "toefl_score" %in% names(toefl_data)) {
          toefl_data$toefl_score <- as.numeric(toefl_data$toefl_score)
        }
        
        return(toefl_data)
        
      }, error = function(e) {
        log_error("Error retrieving TOEFL scores: {e$message}")
        return(data.frame())  # Return empty data frame on error
      })
    }
  )
)
