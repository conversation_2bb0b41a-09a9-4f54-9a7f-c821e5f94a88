#' Candidate Scoring Service
#' 
#' This service handles all candidate scoring logic including configuration
#' retrieval, score calculation, and result persistence. It implements the
#' business logic for candidate evaluation.
#' 
#' <AUTHOR> Team
#' @version 2.0.0

library(logger)
library(dplyr)
library(jsonlite)
library(R6)

# Source required dependencies
source("R/repositories/database_manager.R")
source("R/repositories/candidate_repository.R")
source("R/repositories/configuration_repository.R")
source("R/models/candidate_models.R")
source("R/utils/scoring_algorithms.R")

#' Candidate Scoring Service Class
#' 
#' Provides comprehensive candidate scoring functionality
CandidateScoringService <- R6::R6Class(
  "CandidateScoringService",
  
  private = list(
    .candidate_repo = NULL,
    .config_repo = NULL,
    
    validate_processing_params = function(params) {
      required_fields <- c("job_vacancy_id", "schema", "recalculate_all")
      missing_fields <- setdiff(required_fields, names(params))
      
      if (length(missing_fields) > 0) {
        stop(paste("Missing required parameters:", paste(missing_fields, collapse = ", ")))
      }
      
      if (!is.integer(params$job_vacancy_id) || params$job_vacancy_id <= 0) {
        stop("job_vacancy_id must be a positive integer")
      }
      
      if (!is.logical(params$recalculate_all)) {
        stop("recalculate_all must be a logical value")
      }
    },
    
    calculate_candidate_scores = function(candidates, config) {
      if (nrow(candidates) == 0) {
        log_info("No candidates to score")
        return(data.frame())
      }
      
      log_info("Calculating scores for {nrow(candidates)} candidates")
      
      # Apply scoring algorithms
      scored_candidates <- candidates %>%
        rowwise() %>%
        mutate(
          education_score = calculate_education_score(
            education = Education,
            major = Major,
            config = config$Education
          ),
          experience_score = calculate_experience_score(
            experience = Experience,
            industry = Industry,
            config = config$Working_Experience
          ),
          gpa_score = calculate_gpa_score(
            gpa = GPA,
            education = Education,
            config = config$GPA
          ),
          age_score = calculate_age_score(
            age = age,
            config = config$Age
          ),
          toefl_score = calculate_toefl_score(
            toefl = toefl_score,
            config = config$TOEFL
          )
        ) %>%
        ungroup()
      
      # Calculate weighted final scores
      scored_candidates <- scored_candidates %>%
        mutate(
          total_score = calculate_weighted_score(
            education_score = education_score,
            experience_score = experience_score,
            gpa_score = gpa_score,
            age_score = age_score,
            toefl_score = toefl_score,
            weights = config$weights
          ),
          status = determine_pass_status(
            score = total_score,
            cutoff = config$cutoff_value
          )
        )
      
      return(scored_candidates)
    }
  ),
  
  public = list(
    #' Initialize the scoring service
    initialize = function() {
      private$.candidate_repo <- CandidateRepository$new()
      private$.config_repo <- ConfigurationRepository$new()
      log_info("Candidate scoring service initialized")
    },
    
    #' Process candidates for scoring
    #' @param params Processing parameters including job_vacancy_id, schema, recalculate_all
    #' @return List with results and metadata
    process_candidates = function(params) {
      tryCatch({
        # Validate parameters
        private$validate_processing_params(params)
        
        log_info("Starting candidate scoring process for job {params$job_vacancy_id}")
        
        # Get job configuration
        config <- private$.config_repo$get_job_configuration(
          params$job_vacancy_id,
          params$schema
        )
        
        if (is.null(config)) {
          stop("Job configuration not found")
        }
        
        # Get candidates to process
        candidates <- private$.candidate_repo$get_candidates_for_scoring(
          job_vacancy_id = params$job_vacancy_id,
          schema = params$schema,
          recalculate_all = params$recalculate_all
        )
        
        if (nrow(candidates) == 0) {
          log_info("No candidates found for scoring")
          return(list(
            results = data.frame(),
            metadata = list(
              total_candidates = 0,
              processing_time = 0
            )
          ))
        }
        
        # Calculate scores
        start_time <- Sys.time()
        scored_candidates <- private$calculate_candidate_scores(candidates, config)
        end_time <- Sys.time()
        
        # Prepare results for persistence
        results_for_db <- scored_candidates %>%
          select(
            user_job_vacancy_id,
            score = total_score,
            status,
            education_score,
            experience_score,
            gpa_score,
            age_score,
            toefl_score
          ) %>%
          filter(!is.na(user_job_vacancy_id))
        
        # Persist results
        if (nrow(results_for_db) > 0) {
          rows_affected <- private$.candidate_repo$save_scoring_results(
            results_for_db,
            params$schema
          )
          log_info("Saved scoring results for {rows_affected} candidates")
        }
        
        # Calculate processing metadata
        processing_time <- as.numeric(difftime(end_time, start_time, units = "secs"))
        
        metadata <- list(
          total_candidates = nrow(candidates),
          scored_candidates = nrow(scored_candidates),
          processing_time_seconds = round(processing_time, 2),
          configuration_used = list(
            cutoff_value = config$cutoff_value,
            weights = config$weights
          )
        )
        
        return(list(
          results = results_for_db,
          metadata = metadata
        ))
        
      }, error = function(e) {
        log_error("Error in candidate scoring process: {e$message}")
        stop(e$message)
      })
    },
    
    #' Get job configuration
    #' @param job_vacancy_id Job vacancy identifier
    #' @param schema Database schema
    #' @return Job configuration or NULL if not found
    get_job_configuration = function(job_vacancy_id, schema) {
      tryCatch({
        config <- private$.config_repo$get_job_configuration(job_vacancy_id, schema)
        
        if (is.null(config)) {
          log_warn("Configuration not found for job {job_vacancy_id} in schema {schema}")
          return(NULL)
        }
        
        # Add metadata
        config$metadata <- list(
          retrieved_at = format(Sys.time(), "%Y-%m-%dT%H:%M:%S%z"),
          job_vacancy_id = job_vacancy_id,
          schema = schema
        )
        
        return(config)
        
      }, error = function(e) {
        log_error("Error retrieving job configuration: {e$message}")
        stop(e$message)
      })
    },
    
    #' Validate scoring configuration
    #' @param config Configuration object to validate
    #' @return List with validation results
    validate_configuration = function(config) {
      validation_results <- list(
        valid = TRUE,
        errors = list(),
        warnings = list()
      )
      
      tryCatch({
        # Check required sections
        required_sections <- c("Education", "Working_Experience", "GPA", "Age")
        missing_sections <- setdiff(required_sections, names(config))
        
        if (length(missing_sections) > 0) {
          validation_results$valid <- FALSE
          validation_results$errors <- append(
            validation_results$errors,
            paste("Missing configuration sections:", paste(missing_sections, collapse = ", "))
          )
        }
        
        # Validate weights
        if ("weights" %in% names(config)) {
          weight_sum <- sum(unlist(config$weights), na.rm = TRUE)
          if (abs(weight_sum - 100) > 0.01) {  # Allow small floating point differences
            validation_results$warnings <- append(
              validation_results$warnings,
              paste("Weight sum is", weight_sum, "but should be 100")
            )
          }
        } else {
          validation_results$errors <- append(
            validation_results$errors,
            "Missing weights configuration"
          )
        }
        
        # Validate cutoff value
        if ("cutoff_value" %in% names(config)) {
          cutoff <- config$cutoff_value
          if (!is.numeric(cutoff) || cutoff < 0 || cutoff > 100) {
            validation_results$valid <- FALSE
            validation_results$errors <- append(
              validation_results$errors,
              "Cutoff value must be between 0 and 100"
            )
          }
        } else {
          validation_results$warnings <- append(
            validation_results$warnings,
            "Missing cutoff value, using default"
          )
        }
        
        return(validation_results)
        
      }, error = function(e) {
        validation_results$valid <- FALSE
        validation_results$errors <- append(
          validation_results$errors,
          paste("Configuration validation error:", e$message)
        )
        return(validation_results)
      })
    },
    
    #' Get scoring statistics for a job
    #' @param job_vacancy_id Job vacancy identifier
    #' @param schema Database schema
    #' @return Scoring statistics
    get_scoring_statistics = function(job_vacancy_id, schema) {
      tryCatch({
        stats <- private$.candidate_repo$get_scoring_statistics(job_vacancy_id, schema)
        
        # Add calculated metrics
        if (!is.null(stats) && nrow(stats) > 0) {
          stats$pass_rate <- if (stats$total_candidates > 0) {
            round((stats$passed_candidates / stats$total_candidates) * 100, 2)
          } else {
            0
          }
        }
        
        return(stats)
        
      }, error = function(e) {
        log_error("Error retrieving scoring statistics: {e$message}")
        stop(e$message)
      })
    }
  )
)
