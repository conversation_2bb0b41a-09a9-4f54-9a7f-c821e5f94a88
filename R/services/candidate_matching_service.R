#' Candidate Matching Service
#' 
#' This service handles advanced candidate matching logic including baseline
#' requirements, compatibility scoring, and ranking algorithms.
#' 
#' <AUTHOR> Team
#' @version 2.0.0

library(logger)
library(dplyr)
library(R6)

# Source required dependencies
source("R/repositories/database_manager.R")
source("R/repositories/candidate_repository.R")
source("R/repositories/configuration_repository.R")
source("R/utils/matching_algorithms.R")

#' Candidate Matching Service Class
#' 
#' Provides comprehensive candidate matching functionality
CandidateMatchingService <- R6::R6Class(
  "CandidateMatchingService",
  
  private = list(
    .candidate_repo = NULL,
    .config_repo = NULL,
    
    validate_matching_params = function(params) {
      required_fields <- c("job_vacancy_id", "schema", "recalculate_all")
      missing_fields <- setdiff(required_fields, names(params))
      
      if (length(missing_fields) > 0) {
        stop(paste("Missing required parameters:", paste(missing_fields, collapse = ", ")))
      }
      
      if (!is.integer(params$job_vacancy_id) || params$job_vacancy_id <= 0) {
        stop("job_vacancy_id must be a positive integer")
      }
      
      if (!is.logical(params$recalculate_all)) {
        stop("recalculate_all must be a logical value")
      }
    },
    
    calculate_matching_scores = function(candidates, baseline, config) {
      if (nrow(candidates) == 0) {
        log_info("No candidates to match")
        return(data.frame())
      }
      
      log_info("Calculating matching scores for {nrow(candidates)} candidates")
      
      # Apply matching algorithms
      matched_candidates <- candidates %>%
        rowwise() %>%
        mutate(
          education_match = calculate_education_match(
            education = Education,
            required_education = baseline$education_level,
            config = config
          ),
          experience_match = calculate_experience_match(
            experience = Experience,
            industry = Industry,
            required_industry = baseline$previous_job_industry,
            config = config
          ),
          salary_match = calculate_salary_match(
            expected_salary = Expected_Salary,
            min_salary = baseline$minimum_salary,
            max_salary = baseline$maximum_salary,
            config = config
          ),
          location_match = calculate_location_match(
            candidate_location = Domisili,
            required_location = baseline$domicile,
            config = config
          ),
          skills_match = calculate_skills_match(
            candidate_skills = Skillntools,
            required_skills = baseline$tools_and_competencies,
            config = config
          )
        ) %>%
        ungroup()
      
      # Calculate overall matching score
      matched_candidates <- matched_candidates %>%
        mutate(
          main_score = calculate_overall_match_score(
            education_match = education_match,
            experience_match = experience_match,
            salary_match = salary_match,
            location_match = location_match,
            skills_match = skills_match,
            weights = config$matching_weights
          ),
          match_status = determine_match_status(
            score = main_score,
            threshold = config$matching_threshold
          )
        )
      
      return(matched_candidates)
    }
  ),
  
  public = list(
    #' Initialize the matching service
    initialize = function() {
      private$.candidate_repo <- CandidateRepository$new()
      private$.config_repo <- ConfigurationRepository$new()
      log_info("Candidate matching service initialized")
    },
    
    #' Process candidates for matching
    #' @param params Processing parameters including job_vacancy_id, schema, recalculate_all
    #' @return List with results and metadata
    process_matching = function(params) {
      tryCatch({
        # Validate parameters
        private$validate_matching_params(params)
        
        log_info("Starting candidate matching process for job {params$job_vacancy_id}")
        
        # Get job baseline requirements
        baseline <- private$.config_repo$get_job_baseline(
          params$job_vacancy_id,
          params$schema
        )
        
        if (is.null(baseline)) {
          stop("Job baseline requirements not found")
        }
        
        # Get matching configuration
        config <- private$.config_repo$get_job_configuration(
          params$job_vacancy_id,
          params$schema
        )
        
        if (is.null(config)) {
          stop("Job configuration not found")
        }
        
        # Get candidates to process
        candidates <- private$.candidate_repo$get_candidates_for_scoring(
          job_vacancy_id = params$job_vacancy_id,
          schema = params$schema,
          recalculate_all = params$recalculate_all
        )
        
        if (nrow(candidates) == 0) {
          log_info("No candidates found for matching")
          return(list(
            results = data.frame(),
            metadata = list(
              total_candidates = 0,
              processing_time = 0
            )
          ))
        }
        
        # Calculate matching scores
        start_time <- Sys.time()
        matched_candidates <- private$calculate_matching_scores(candidates, baseline, config)
        end_time <- Sys.time()
        
        # Prepare results for persistence
        results_for_db <- matched_candidates %>%
          select(
            user_job_vacancy_id,
            main_score,
            match_status,
            education_match,
            experience_match,
            salary_match,
            location_match,
            skills_match
          ) %>%
          filter(!is.na(user_job_vacancy_id))
        
        # Persist results
        if (nrow(results_for_db) > 0) {
          rows_affected <- self$save_matching_results(
            results_for_db,
            params$schema
          )
          log_info("Saved matching results for {rows_affected} candidates")
        }
        
        # Calculate processing metadata
        processing_time <- as.numeric(difftime(end_time, start_time, units = "secs"))
        
        metadata <- list(
          total_candidates = nrow(candidates),
          matched_candidates = nrow(matched_candidates),
          processing_time_seconds = round(processing_time, 2),
          baseline_used = baseline,
          matching_threshold = config$matching_threshold %||% 50
        )
        
        return(list(
          results = results_for_db,
          metadata = metadata
        ))
        
      }, error = function(e) {
        log_error("Error in candidate matching process: {e$message}")
        stop(e$message)
      })
    },
    
    #' Save matching results to database
    #' @param results Data frame with matching results
    #' @param schema Database schema
    #' @return Number of rows affected
    save_matching_results = function(results, schema) {
      tryCatch({
        if (nrow(results) == 0) {
          log_info("No matching results to save")
          return(0)
        }
        
        # Use database manager for transaction
        db_manager <- get_db_manager()
        db_manager$set_schema(schema, "write")
        
        rows_affected <- db_manager$execute_transaction(function(conn) {
          total_rows <- 0
          
          # Process in batches
          batch_size <- 1000
          num_batches <- ceiling(nrow(results) / batch_size)
          
          for (i in 1:num_batches) {
            start_idx <- (i - 1) * batch_size + 1
            end_idx <- min(i * batch_size, nrow(results))
            batch_data <- results[start_idx:end_idx, ]
            
            # Build batch insert query for matching results
            values_list <- apply(batch_data, 1, function(row) {
              paste0(
                "(", row["user_job_vacancy_id"], ", ",
                row["main_score"], ", ",
                "'", row["match_status"], "', ",
                "NOW(), NOW())"
              )
            })
            
            insert_query <- paste0(
              "INSERT INTO user_vacancy_matchmaking_results ",
              "(user_job_vacancy_id, main_score, match_status, created_at, updated_at) VALUES ",
              paste(values_list, collapse = ", "),
              " ON CONFLICT (user_job_vacancy_id) DO UPDATE SET ",
              "main_score = EXCLUDED.main_score, ",
              "match_status = EXCLUDED.match_status, ",
              "updated_at = EXCLUDED.updated_at"
            )
            
            batch_rows <- dbExecute(conn, insert_query)
            total_rows <- total_rows + batch_rows
            
            log_debug("Saved matching batch {i}/{num_batches}: {batch_rows} rows")
          }
          
          return(total_rows)
        })
        
        log_info("Successfully saved {rows_affected} matching results")
        return(rows_affected)
        
      }, error = function(e) {
        log_error("Error saving matching results: {e$message}")
        stop(paste("Failed to save matching results:", e$message))
      })
    },
    
    #' Get matching statistics for a job
    #' @param job_vacancy_id Job vacancy identifier
    #' @param schema Database schema
    #' @return Matching statistics
    get_matching_statistics = function(job_vacancy_id, schema) {
      tryCatch({
        db_manager <- get_db_manager()
        db_manager$set_schema(schema, "read")
        
        stats_query <- "
          SELECT 
            COUNT(*) as total_candidates,
            COUNT(CASE WHEN uvmr.match_status = 'MATCHED' THEN 1 END) as matched_candidates,
            COUNT(CASE WHEN uvmr.match_status = 'NOT_MATCHED' THEN 1 END) as not_matched_candidates,
            AVG(uvmr.main_score) as average_match_score,
            MIN(uvmr.main_score) as min_match_score,
            MAX(uvmr.main_score) as max_match_score,
            STDDEV(uvmr.main_score) as match_score_stddev
          FROM candidate_datas cd
          LEFT JOIN user_vacancy_matchmaking_results uvmr 
            ON cd.user_job_vacancy_id = uvmr.user_job_vacancy_id
          WHERE cd.job_vacancy_id = ?job_vacancy_id
        "
        
        stats <- db_manager$execute_query(
          stats_query,
          list(job_vacancy_id = job_vacancy_id),
          "read"
        )
        
        # Convert to proper data types and add calculated metrics
        if (nrow(stats) > 0) {
          stats <- stats %>%
            mutate(
              total_candidates = as.integer(total_candidates),
              matched_candidates = as.integer(coalesce(matched_candidates, 0)),
              not_matched_candidates = as.integer(coalesce(not_matched_candidates, 0)),
              average_match_score = round(as.numeric(coalesce(average_match_score, 0)), 2),
              min_match_score = round(as.numeric(coalesce(min_match_score, 0)), 2),
              max_match_score = round(as.numeric(coalesce(max_match_score, 0)), 2),
              match_score_stddev = round(as.numeric(coalesce(match_score_stddev, 0)), 2),
              match_rate = if (total_candidates > 0) {
                round((matched_candidates / total_candidates) * 100, 2)
              } else {
                0
              }
            )
        }
        
        return(stats)
        
      }, error = function(e) {
        log_error("Error retrieving matching statistics: {e$message}")
        stop(paste("Failed to retrieve matching statistics:", e$message))
      })
    },
    
    #' Get top matched candidates
    #' @param job_vacancy_id Job vacancy identifier
    #' @param schema Database schema
    #' @param limit Number of top candidates to return
    #' @return Top matched candidates
    get_top_matches = function(job_vacancy_id, schema, limit = 10) {
      tryCatch({
        db_manager <- get_db_manager()
        db_manager$set_schema(schema, "read")
        
        top_matches_query <- "
          SELECT 
            cd.user_job_vacancy_id,
            cd.fullname,
            cd.degree,
            cd.major,
            cd.gpa,
            cd.calculated_yoe as experience,
            cd.industry,
            uvmr.main_score,
            uvmr.match_status
          FROM candidate_datas cd
          JOIN user_vacancy_matchmaking_results uvmr 
            ON cd.user_job_vacancy_id = uvmr.user_job_vacancy_id
          WHERE cd.job_vacancy_id = ?job_vacancy_id
            AND uvmr.match_status = 'MATCHED'
          ORDER BY uvmr.main_score DESC
          LIMIT ?limit
        "
        
        top_matches <- db_manager$execute_query(
          top_matches_query,
          list(job_vacancy_id = job_vacancy_id, limit = limit),
          "read"
        )
        
        return(top_matches)
        
      }, error = function(e) {
        log_error("Error retrieving top matches: {e$message}")
        stop(paste("Failed to retrieve top matches:", e$message))
      })
    }
  )
)
