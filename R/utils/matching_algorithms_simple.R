#' Simplified Matching Algorithms (No External Dependencies)
#' 
#' This module contains matching algorithms without external package dependencies
#' for testing the refactored system core functionality.
#' 
#' <AUTHOR> Team
#' @version 2.0.0

#' Calculate Education Match Score
#' 
#' Determines how well candidate's education matches job requirements
#' 
#' @param education Candidate's education level
#' @param required_education Required education level
#' @param config Matching configuration
#' @return Education match score (0-100)
calculate_education_match <- function(education, required_education, config) {
  if (is.null(education) || is.na(education) || education == "") {
    return(0)
  }
  
  if (is.null(required_education) || is.na(required_education) || required_education == "") {
    return(50)  # Neutral score if no requirement specified
  }
  
  # Normalize education levels
  education <- toupper(trimws(education))
  required_education <- toupper(trimws(required_education))
  
  # Education hierarchy (higher number = higher level)
  education_levels <- list(
    "SMA" = 1, "SMK" = 1,
    "D1" = 2, "D2" = 2, "D3" = 3, "D4" = 4,
    "S1" = 5, "S2" = 6, "S3" = 7
  )
  
  candidate_level <- education_levels[[education]]
  required_level <- education_levels[[required_education]]
  
  if (is.null(candidate_level)) candidate_level <- 0
  if (is.null(required_level)) required_level <- 0
  
  if (candidate_level == 0 || required_level == 0) {
    return(25)  # Low score for unknown education levels
  }
  
  # Calculate match score
  if (candidate_level >= required_level) {
    # Candidate meets or exceeds requirement
    if (candidate_level == required_level) {
      return(100)  # Perfect match
    } else if (candidate_level == required_level + 1) {
      return(90)   # One level higher
    } else {
      return(80)   # Multiple levels higher (might be overqualified)
    }
  } else {
    # Candidate doesn't meet requirement
    level_diff <- required_level - candidate_level
    if (level_diff == 1) {
      return(60)   # One level below
    } else if (level_diff == 2) {
      return(30)   # Two levels below
    } else {
      return(10)   # Multiple levels below
    }
  }
}

#' Calculate Experience Match Score
#' 
#' Determines how well candidate's experience matches job requirements
#' 
#' @param experience Candidate's years of experience
#' @param industry Candidate's industry background
#' @param required_industry Required industry background
#' @param config Matching configuration
#' @return Experience match score (0-100)
calculate_experience_match <- function(experience, industry, required_industry, config) {
  if (is.null(experience) || is.na(experience)) {
    return(0)
  }
  
  experience <- as.numeric(experience)
  if (is.na(experience) || experience < 0) {
    return(0)
  }
  
  # Base score from experience years (assuming 3+ years is ideal)
  base_score <- if (experience >= 5) {
    100
  } else if (experience >= 3) {
    80
  } else if (experience >= 2) {
    60
  } else if (experience >= 1) {
    40
  } else if (experience > 0) {
    20
  } else {
    0
  }
  
  # Industry relevance bonus/penalty
  industry_bonus <- calculate_industry_match(industry, required_industry)
  
  # Combine scores (cap at 100)
  final_score <- min(100, base_score + industry_bonus)
  
  return(final_score)
}

#' Calculate Industry Match Score
#' 
#' Determines industry relevance between candidate and job
#' 
#' @param candidate_industry Candidate's industry background
#' @param required_industry Required industry background
#' @return Industry match bonus/penalty (-20 to +20)
calculate_industry_match <- function(candidate_industry, required_industry) {
  if (is.null(candidate_industry) || is.na(candidate_industry) || 
      is.null(required_industry) || is.na(required_industry)) {
    return(0)  # Neutral if either is missing
  }
  
  candidate_industry <- tolower(trimws(candidate_industry))
  required_industry <- tolower(trimws(required_industry))
  
  if (candidate_industry == "" || required_industry == "") {
    return(0)
  }
  
  # Exact match
  if (candidate_industry == required_industry) {
    return(20)
  }
  
  # Partial match (contains keywords)
  if (grepl(candidate_industry, required_industry, fixed = TRUE) ||
      grepl(required_industry, candidate_industry, fixed = TRUE)) {
    return(10)
  }
  
  # Related industries
  tech_industries <- c("technology", "software", "it", "tech", "digital", "computer")
  finance_industries <- c("finance", "banking", "financial", "investment", "insurance")
  
  candidate_is_tech <- any(sapply(tech_industries, function(x) grepl(x, candidate_industry)))
  required_is_tech <- any(sapply(tech_industries, function(x) grepl(x, required_industry)))
  
  candidate_is_finance <- any(sapply(finance_industries, function(x) grepl(x, candidate_industry)))
  required_is_finance <- any(sapply(finance_industries, function(x) grepl(x, required_industry)))
  
  if ((candidate_is_tech && required_is_tech) || (candidate_is_finance && required_is_finance)) {
    return(15)
  }
  
  # No match
  return(-10)
}

#' Calculate Overall Match Score
#' 
#' Combines individual match scores into overall compatibility score
#' 
#' @param education_match Education match score
#' @param experience_match Experience match score
#' @param salary_match Salary match score
#' @param location_match Location match score
#' @param skills_match Skills match score
#' @param weights Weight configuration for different factors
#' @return Overall match score (0-100)
calculate_overall_match_score <- function(education_match, experience_match, salary_match,
                                        location_match, skills_match, weights) {
  # Default weights if not provided
  if (is.null(weights)) {
    weights <- list(
      education = 20,
      experience = 30,
      salary = 15,
      location = 15,
      skills = 20
    )
  }
  
  # Ensure all scores are numeric
  education_match <- as.numeric(education_match)
  experience_match <- as.numeric(experience_match)
  salary_match <- as.numeric(salary_match)
  location_match <- as.numeric(location_match)
  skills_match <- as.numeric(skills_match)
  
  if (is.na(education_match)) education_match <- 0
  if (is.na(experience_match)) experience_match <- 0
  if (is.na(salary_match)) salary_match <- 0
  if (is.na(location_match)) location_match <- 0
  if (is.na(skills_match)) skills_match <- 0
  
  # Calculate weighted sum
  weighted_sum <- (
    education_match * (weights$education) +
    experience_match * (weights$experience) +
    salary_match * (weights$salary) +
    location_match * (weights$location) +
    skills_match * (weights$skills)
  )
  
  # Calculate total weight
  total_weight <- weights$education + weights$experience + weights$salary + weights$location + weights$skills
  
  if (total_weight == 0) {
    return(0)
  }
  
  # Normalize to 0-100 scale
  final_score <- weighted_sum / total_weight
  
  # Ensure score is within valid range
  final_score <- max(0, min(100, final_score))
  
  return(round(final_score, 2))
}

#' Determine Match Status
#' 
#' Determines if candidate is a match based on score and threshold
#' 
#' @param score Candidate's overall match score
#' @param threshold Matching threshold
#' @return Match status ("MATCHED" or "NOT_MATCHED")
determine_match_status <- function(score, threshold) {
  if (is.null(score) || is.na(score)) {
    return("NOT_MATCHED")
  }
  
  score <- as.numeric(score)
  threshold <- as.numeric(threshold)
  
  if (is.na(score) || is.na(threshold)) {
    return("NOT_MATCHED")
  }
  
  if (is.null(threshold) || is.na(threshold)) {
    threshold <- 50  # Default threshold
  }
  
  return(if (score >= threshold) "MATCHED" else "NOT_MATCHED")
}
