#' Matching Algorithms
#' 
#' This module contains algorithms for candidate-job matching including
#' compatibility scoring, requirement matching, and ranking functions.
#' 
#' <AUTHOR> Team
#' @version 2.0.0

library(logger)
library(dplyr)

#' Calculate Education Match Score
#' 
#' Determines how well candidate's education matches job requirements
#' 
#' @param education Candidate's education level
#' @param required_education Required education level
#' @param config Matching configuration
#' @return Education match score (0-100)
calculate_education_match <- function(education, required_education, config) {
  tryCatch({
    if (is.null(education) || is.na(education) || education == "") {
      return(0)
    }
    
    if (is.null(required_education) || is.na(required_education) || required_education == "") {
      return(50)  # Neutral score if no requirement specified
    }
    
    # Normalize education levels
    education <- toupper(trimws(education))
    required_education <- toupper(trimws(required_education))
    
    # Education hierarchy (higher number = higher level)
    education_levels <- list(
      "SMA" = 1, "SMK" = 1,
      "D1" = 2, "D2" = 2, "D3" = 3, "D4" = 4,
      "S1" = 5, "S2" = 6, "S3" = 7
    )
    
    candidate_level <- education_levels[[education]] %||% 0
    required_level <- education_levels[[required_education]] %||% 0
    
    if (candidate_level == 0 || required_level == 0) {
      return(25)  # Low score for unknown education levels
    }
    
    # Calculate match score
    if (candidate_level >= required_level) {
      # Candidate meets or exceeds requirement
      if (candidate_level == required_level) {
        return(100)  # Perfect match
      } else if (candidate_level == required_level + 1) {
        return(90)   # One level higher
      } else {
        return(80)   # Multiple levels higher (might be overqualified)
      }
    } else {
      # Candidate doesn't meet requirement
      level_diff <- required_level - candidate_level
      if (level_diff == 1) {
        return(60)   # One level below
      } else if (level_diff == 2) {
        return(30)   # Two levels below
      } else {
        return(10)   # Multiple levels below
      }
    }
    
  }, error = function(e) {
    log_error("Error calculating education match: {e$message}")
    return(0)
  })
}

#' Calculate Experience Match Score
#' 
#' Determines how well candidate's experience matches job requirements
#' 
#' @param experience Candidate's years of experience
#' @param industry Candidate's industry background
#' @param required_industry Required industry background
#' @param config Matching configuration
#' @return Experience match score (0-100)
calculate_experience_match <- function(experience, industry, required_industry, config) {
  tryCatch({
    if (is.null(experience) || is.na(experience)) {
      return(0)
    }
    
    experience <- as.numeric(experience)
    if (is.na(experience) || experience < 0) {
      return(0)
    }
    
    # Base score from experience years (assuming 3+ years is ideal)
    base_score <- if (experience >= 5) {
      100
    } else if (experience >= 3) {
      80
    } else if (experience >= 2) {
      60
    } else if (experience >= 1) {
      40
    } else if (experience > 0) {
      20
    } else {
      0
    }
    
    # Industry relevance bonus/penalty
    industry_bonus <- calculate_industry_match(industry, required_industry)
    
    # Combine scores (cap at 100)
    final_score <- min(100, base_score + industry_bonus)
    
    return(final_score)
    
  }, error = function(e) {
    log_error("Error calculating experience match: {e$message}")
    return(0)
  })
}

#' Calculate Industry Match Score
#' 
#' Determines industry relevance between candidate and job
#' 
#' @param candidate_industry Candidate's industry background
#' @param required_industry Required industry background
#' @return Industry match bonus/penalty (-20 to +20)
calculate_industry_match <- function(candidate_industry, required_industry) {
  if (is.null(candidate_industry) || is.na(candidate_industry) || 
      is.null(required_industry) || is.na(required_industry)) {
    return(0)  # Neutral if either is missing
  }
  
  candidate_industry <- tolower(trimws(candidate_industry))
  required_industry <- tolower(trimws(required_industry))
  
  if (candidate_industry == "" || required_industry == "") {
    return(0)
  }
  
  # Exact match
  if (candidate_industry == required_industry) {
    return(20)
  }
  
  # Partial match (contains keywords)
  if (grepl(candidate_industry, required_industry, fixed = TRUE) ||
      grepl(required_industry, candidate_industry, fixed = TRUE)) {
    return(10)
  }
  
  # Related industries (this could be expanded with a mapping table)
  tech_industries <- c("technology", "software", "it", "tech", "digital", "computer")
  finance_industries <- c("finance", "banking", "financial", "investment", "insurance")
  healthcare_industries <- c("healthcare", "medical", "health", "pharmaceutical", "hospital")
  
  if (any(sapply(tech_industries, function(x) grepl(x, candidate_industry))) &&
      any(sapply(tech_industries, function(x) grepl(x, required_industry)))) {
    return(15)
  }
  
  if (any(sapply(finance_industries, function(x) grepl(x, candidate_industry))) &&
      any(sapply(finance_industries, function(x) grepl(x, required_industry)))) {
    return(15)
  }
  
  if (any(sapply(healthcare_industries, function(x) grepl(x, candidate_industry))) &&
      any(sapply(healthcare_industries, function(x) grepl(x, required_industry)))) {
    return(15)
  }
  
  # No match
  return(-10)
}

#' Calculate Salary Match Score
#' 
#' Determines salary expectation compatibility
#' 
#' @param expected_salary Candidate's expected salary
#' @param min_salary Job's minimum salary
#' @param max_salary Job's maximum salary
#' @param config Matching configuration
#' @return Salary match score (0-100)
calculate_salary_match <- function(expected_salary, min_salary, max_salary, config) {
  tryCatch({
    if (is.null(expected_salary) || is.na(expected_salary)) {
      return(50)  # Neutral if no expectation specified
    }
    
    expected_salary <- as.numeric(expected_salary)
    min_salary <- as.numeric(min_salary %||% 0)
    max_salary <- as.numeric(max_salary %||% Inf)
    
    if (is.na(expected_salary) || expected_salary <= 0) {
      return(50)
    }
    
    # Perfect match if within range
    if (expected_salary >= min_salary && expected_salary <= max_salary) {
      return(100)
    }
    
    # Calculate how far outside the range
    if (expected_salary < min_salary) {
      # Candidate expects less (good for employer)
      diff_percent <- (min_salary - expected_salary) / min_salary * 100
      if (diff_percent <= 10) {
        return(95)   # Within 10% below minimum
      } else if (diff_percent <= 20) {
        return(85)   # Within 20% below minimum
      } else {
        return(70)   # More than 20% below (might indicate underqualification)
      }
    } else {
      # Candidate expects more (challenging for employer)
      diff_percent <- (expected_salary - max_salary) / max_salary * 100
      if (diff_percent <= 10) {
        return(80)   # Within 10% above maximum
      } else if (diff_percent <= 20) {
        return(60)   # Within 20% above maximum
      } else if (diff_percent <= 30) {
        return(40)   # Within 30% above maximum
      } else {
        return(20)   # More than 30% above maximum
      }
    }
    
  }, error = function(e) {
    log_error("Error calculating salary match: {e$message}")
    return(0)
  })
}

#' Calculate Location Match Score
#' 
#' Determines location compatibility between candidate and job
#' 
#' @param candidate_location Candidate's location/domicile
#' @param required_location Required job location
#' @param config Matching configuration
#' @return Location match score (0-100)
calculate_location_match <- function(candidate_location, required_location, config) {
  tryCatch({
    if (is.null(candidate_location) || is.na(candidate_location) ||
        is.null(required_location) || is.na(required_location)) {
      return(50)  # Neutral if either is missing
    }
    
    candidate_location <- tolower(trimws(candidate_location))
    required_location <- tolower(trimws(required_location))
    
    if (candidate_location == "" || required_location == "") {
      return(50)
    }
    
    # Exact match
    if (candidate_location == required_location) {
      return(100)
    }
    
    # Partial match (same city/region)
    if (grepl(candidate_location, required_location, fixed = TRUE) ||
        grepl(required_location, candidate_location, fixed = TRUE)) {
      return(80)
    }
    
    # Same province/state (this would need a mapping table in real implementation)
    # For now, check if they share common words
    candidate_words <- strsplit(candidate_location, "\\s+")[[1]]
    required_words <- strsplit(required_location, "\\s+")[[1]]
    
    common_words <- intersect(candidate_words, required_words)
    if (length(common_words) > 0) {
      return(60)
    }
    
    # Different locations
    return(30)
    
  }, error = function(e) {
    log_error("Error calculating location match: {e$message}")
    return(0)
  })
}

#' Calculate Skills Match Score
#' 
#' Determines how well candidate's skills match job requirements
#' 
#' @param candidate_skills Candidate's skills and tools
#' @param required_skills Required skills and competencies
#' @param config Matching configuration
#' @return Skills match score (0-100)
calculate_skills_match <- function(candidate_skills, required_skills, config) {
  tryCatch({
    if (is.null(candidate_skills) || is.na(candidate_skills) ||
        is.null(required_skills) || is.na(required_skills)) {
      return(50)  # Neutral if either is missing
    }
    
    candidate_skills <- tolower(trimws(candidate_skills))
    required_skills <- tolower(trimws(required_skills))
    
    if (candidate_skills == "" || required_skills == "") {
      return(50)
    }
    
    # Split skills into individual items
    candidate_skill_list <- strsplit(candidate_skills, "[,;|]")[[1]]
    required_skill_list <- strsplit(required_skills, "[,;|]")[[1]]
    
    # Clean and normalize skills
    candidate_skill_list <- trimws(candidate_skill_list)
    required_skill_list <- trimws(required_skill_list)
    
    # Remove empty strings
    candidate_skill_list <- candidate_skill_list[candidate_skill_list != ""]
    required_skill_list <- required_skill_list[required_skill_list != ""]
    
    if (length(required_skill_list) == 0) {
      return(50)  # No specific requirements
    }
    
    # Calculate exact matches
    exact_matches <- sum(candidate_skill_list %in% required_skill_list)
    
    # Calculate partial matches (fuzzy matching)
    partial_matches <- 0
    for (req_skill in required_skill_list) {
      if (!req_skill %in% candidate_skill_list) {
        # Check for partial matches
        for (cand_skill in candidate_skill_list) {
          if (grepl(req_skill, cand_skill, fixed = TRUE) ||
              grepl(cand_skill, req_skill, fixed = TRUE)) {
            partial_matches <- partial_matches + 0.5
            break
          }
        }
      }
    }
    
    # Calculate match percentage
    total_matches <- exact_matches + partial_matches
    match_percentage <- (total_matches / length(required_skill_list)) * 100
    
    # Cap at 100
    return(min(100, round(match_percentage)))
    
  }, error = function(e) {
    log_error("Error calculating skills match: {e$message}")
    return(0)
  })
}

#' Calculate Overall Match Score
#' 
#' Combines individual match scores into overall compatibility score
#' 
#' @param education_match Education match score
#' @param experience_match Experience match score
#' @param salary_match Salary match score
#' @param location_match Location match score
#' @param skills_match Skills match score
#' @param weights Weight configuration for different factors
#' @return Overall match score (0-100)
calculate_overall_match_score <- function(education_match, experience_match, salary_match,
                                        location_match, skills_match, weights) {
  tryCatch({
    # Default weights if not provided
    if (is.null(weights)) {
      weights <- list(
        education = 20,
        experience = 30,
        salary = 15,
        location = 15,
        skills = 20
      )
    }
    
    # Ensure all scores are numeric
    education_match <- as.numeric(education_match %||% 0)
    experience_match <- as.numeric(experience_match %||% 0)
    salary_match <- as.numeric(salary_match %||% 0)
    location_match <- as.numeric(location_match %||% 0)
    skills_match <- as.numeric(skills_match %||% 0)
    
    # Calculate weighted sum
    weighted_sum <- (
      education_match * (weights$education %||% 20) +
      experience_match * (weights$experience %||% 30) +
      salary_match * (weights$salary %||% 15) +
      location_match * (weights$location %||% 15) +
      skills_match * (weights$skills %||% 20)
    )
    
    # Calculate total weight
    total_weight <- sum(unlist(weights), na.rm = TRUE)
    
    if (total_weight == 0) {
      return(0)
    }
    
    # Normalize to 0-100 scale
    final_score <- weighted_sum / total_weight
    
    # Ensure score is within valid range
    final_score <- max(0, min(100, final_score))
    
    return(round(final_score, 2))
    
  }, error = function(e) {
    log_error("Error calculating overall match score: {e$message}")
    return(0)
  })
}

#' Determine Match Status
#' 
#' Determines if candidate is a match based on score and threshold
#' 
#' @param score Candidate's overall match score
#' @param threshold Matching threshold
#' @return Match status ("MATCHED" or "NOT_MATCHED")
determine_match_status <- function(score, threshold) {
  tryCatch({
    if (is.null(score) || is.na(score)) {
      return("NOT_MATCHED")
    }
    
    score <- as.numeric(score)
    threshold <- as.numeric(threshold %||% 50)
    
    if (is.na(score) || is.na(threshold)) {
      return("NOT_MATCHED")
    }
    
    return(if (score >= threshold) "MATCHED" else "NOT_MATCHED")
    
  }, error = function(e) {
    log_error("Error determining match status: {e$message}")
    return("NOT_MATCHED")
  })
}
