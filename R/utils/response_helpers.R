#' Response Helper Functions
#' 
#' This module provides standardized response formatting functions for the API
#' to ensure consistent response structure across all endpoints.
#' 
#' <AUTHOR> Team
#' @version 2.0.0

library(jsonlite)
library(logger)

#' Create Success Response
#' 
#' Creates a standardized success response structure
#' 
#' @param data The data to include in the response
#' @param message Optional success message
#' @param metadata Optional metadata (pagination, etc.)
#' @param request_id Optional request identifier
#' @return List representing the success response
create_success_response <- function(data, message = "Success", metadata = NULL, request_id = NULL) {
  response <- list(
    success = TRUE,
    message = message,
    data = data,
    timestamp = format(Sys.time(), "%Y-%m-%dT%H:%M:%S%z")
  )
  
  if (!is.null(metadata)) {
    response$metadata <- metadata
  }
  
  if (!is.null(request_id)) {
    response$request_id <- request_id
  }
  
  return(response)
}

#' Create Error Response
#' 
#' Creates a standardized error response structure
#' 
#' @param error_type The type/category of error
#' @param message Detailed error message
#' @param request_id Optional request identifier
#' @param details Optional additional error details
#' @return List representing the error response
create_error_response <- function(error_type, message, request_id = NULL, details = NULL) {
  response <- list(
    success = FALSE,
    error = list(
      type = error_type,
      message = message,
      timestamp = format(Sys.time(), "%Y-%m-%dT%H:%M:%S%z")
    )
  )
  
  if (!is.null(details)) {
    response$error$details <- details
  }
  
  if (!is.null(request_id)) {
    response$request_id <- request_id
  }
  
  return(response)
}

#' Create Validation Error Response
#' 
#' Creates a standardized validation error response
#' 
#' @param validation_errors List of validation errors
#' @param request_id Optional request identifier
#' @return List representing the validation error response
create_validation_error_response <- function(validation_errors, request_id = NULL) {
  create_error_response(
    error_type = "Validation Error",
    message = "Input validation failed",
    request_id = request_id,
    details = list(
      validation_errors = validation_errors
    )
  )
}

#' Create Paginated Response
#' 
#' Creates a response with pagination metadata
#' 
#' @param data The data items for the current page
#' @param page Current page number
#' @param per_page Number of items per page
#' @param total_items Total number of items
#' @param message Optional success message
#' @param request_id Optional request identifier
#' @return List representing the paginated response
create_paginated_response <- function(data, page, per_page, total_items, 
                                    message = "Success", request_id = NULL) {
  total_pages <- ceiling(total_items / per_page)
  
  metadata <- list(
    pagination = list(
      current_page = page,
      per_page = per_page,
      total_items = total_items,
      total_pages = total_pages,
      has_next = page < total_pages,
      has_previous = page > 1
    )
  )
  
  create_success_response(
    data = data,
    message = message,
    metadata = metadata,
    request_id = request_id
  )
}

#' Format Processing Results
#' 
#' Formats candidate processing results with summary statistics
#' 
#' @param results Data frame containing processing results
#' @param processing_type Type of processing performed
#' @param request_id Optional request identifier
#' @return Formatted response with results and summary
format_processing_results <- function(results, processing_type, request_id = NULL) {
  if (is.null(results) || nrow(results) == 0) {
    return(create_success_response(
      data = list(
        results = list(),
        summary = list(
          total_processed = 0,
          processing_type = processing_type
        )
      ),
      message = "No candidates found for processing",
      request_id = request_id
    ))
  }
  
  # Calculate summary statistics
  summary_stats <- list(
    total_processed = nrow(results),
    processing_type = processing_type,
    processing_time = format(Sys.time(), "%Y-%m-%dT%H:%M:%S%z")
  )
  
  # Add type-specific statistics
  if (processing_type == "scoring") {
    if ("score" %in% names(results)) {
      summary_stats$score_statistics <- list(
        mean_score = round(mean(results$score, na.rm = TRUE), 2),
        median_score = round(median(results$score, na.rm = TRUE), 2),
        min_score = round(min(results$score, na.rm = TRUE), 2),
        max_score = round(max(results$score, na.rm = TRUE), 2)
      )
    }
    
    if ("status" %in% names(results)) {
      status_counts <- table(results$status)
      summary_stats$status_distribution <- as.list(status_counts)
    }
  } else if (processing_type == "matching") {
    if ("main_score" %in% names(results)) {
      summary_stats$matching_statistics <- list(
        mean_match_score = round(mean(results$main_score, na.rm = TRUE), 2),
        candidates_above_threshold = sum(results$main_score > 50, na.rm = TRUE)
      )
    }
  }
  
  # Convert results to list format for JSON serialization
  results_list <- if (nrow(results) > 0) {
    lapply(1:nrow(results), function(i) {
      row_data <- as.list(results[i, ])
      # Convert any factors to characters
      row_data <- lapply(row_data, function(x) {
        if (is.factor(x)) as.character(x) else x
      })
      row_data
    })
  } else {
    list()
  }
  
  create_success_response(
    data = list(
      results = results_list,
      summary = summary_stats
    ),
    message = paste("Successfully processed", nrow(results), "candidates"),
    request_id = request_id
  )
}

#' Sanitize Data for JSON Response
#' 
#' Cleans data for safe JSON serialization
#' 
#' @param data Data to sanitize
#' @return Sanitized data
sanitize_for_json <- function(data) {
  if (is.data.frame(data)) {
    # Convert factors to characters
    data[] <- lapply(data, function(x) {
      if (is.factor(x)) as.character(x) else x
    })
    
    # Handle NA values
    data[is.na(data)] <- NULL
    
    return(data)
  } else if (is.list(data)) {
    return(lapply(data, sanitize_for_json))
  } else {
    return(data)
  }
}

#' Create Processing Status Response
#' 
#' Creates a response for long-running processing operations
#' 
#' @param job_id Unique identifier for the processing job
#' @param status Current status of the job
#' @param progress Progress percentage (0-100)
#' @param message Status message
#' @param estimated_completion Optional estimated completion time
#' @param request_id Optional request identifier
#' @return Status response
create_processing_status_response <- function(job_id, status, progress = NULL, 
                                            message = NULL, estimated_completion = NULL,
                                            request_id = NULL) {
  status_data <- list(
    job_id = job_id,
    status = status,
    started_at = format(Sys.time(), "%Y-%m-%dT%H:%M:%S%z")
  )
  
  if (!is.null(progress)) {
    status_data$progress_percent = progress
  }
  
  if (!is.null(message)) {
    status_data$message = message
  }
  
  if (!is.null(estimated_completion)) {
    status_data$estimated_completion = estimated_completion
  }
  
  create_success_response(
    data = status_data,
    message = paste("Job", job_id, "is", status),
    request_id = request_id
  )
}

#' Handle API Exceptions
#' 
#' Converts R exceptions to appropriate API responses
#' 
#' @param error The caught error object
#' @param request_id Optional request identifier
#' @return Appropriate error response
handle_api_exception <- function(error, request_id = NULL) {
  error_message <- if (is.character(error)) error else error$message
  
  # Log the error
  log_error("API Exception: {error_message}")
  
  # Determine error type and appropriate response
  if (grepl("validation|invalid", error_message, ignore.case = TRUE)) {
    return(create_error_response(
      error_type = "Validation Error",
      message = error_message,
      request_id = request_id
    ))
  } else if (grepl("not found|missing", error_message, ignore.case = TRUE)) {
    return(create_error_response(
      error_type = "Not Found",
      message = error_message,
      request_id = request_id
    ))
  } else if (grepl("unauthorized|forbidden", error_message, ignore.case = TRUE)) {
    return(create_error_response(
      error_type = "Authorization Error",
      message = error_message,
      request_id = request_id
    ))
  } else {
    return(create_error_response(
      error_type = "Internal Server Error",
      message = if (get_app_config()$is_production()) {
        "An internal error occurred"
      } else {
        error_message
      },
      request_id = request_id
    ))
  }
}
