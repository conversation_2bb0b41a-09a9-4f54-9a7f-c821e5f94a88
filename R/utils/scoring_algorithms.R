#' Scoring Algorithms
#' 
#' This module contains all scoring algorithms used for candidate evaluation.
#' Each algorithm is implemented as a pure function for testability and reusability.
#' 
#' <AUTHOR> Team
#' @version 2.0.0

library(logger)
library(dplyr)

#' Calculate Education Score
#' 
#' Calculates education score based on degree level and major relevance
#' 
#' @param education Candidate's education level
#' @param major Candidate's major/field of study
#' @param config Education scoring configuration
#' @return Numeric education score
calculate_education_score <- function(education, major, config) {
  tryCatch({
    if (is.null(education) || is.na(education) || education == "") {
      return(0)
    }
    
    if (is.null(config) || is.null(config$norma_education)) {
      log_warn("Education configuration missing, using default scoring")
      return(get_default_education_score(education))
    }
    
    # Normalize education level
    education <- toupper(trimws(education))
    
    # Find matching education level in configuration
    norma_education <- config$norma_education
    
    if (is.data.frame(norma_education)) {
      matching_row <- norma_education[norma_education$LastEducation == education, ]
      
      if (nrow(matching_row) > 0) {
        base_score <- matching_row$Score[1]
        
        # Apply major relevance bonus if configured
        if (!is.null(major) && !is.na(major) && major != "") {
          major_bonus <- calculate_major_relevance_bonus(major, config)
          return(base_score + major_bonus)
        }
        
        return(base_score)
      }
    }
    
    # Fallback to default scoring
    return(get_default_education_score(education))
    
  }, error = function(e) {
    log_error("Error calculating education score: {e$message}")
    return(0)
  })
}

#' Get Default Education Score
#' 
#' Provides default education scoring when configuration is unavailable
#' 
#' @param education Education level
#' @return Default education score
get_default_education_score <- function(education) {
  education_scores <- list(
    "S3" = 5,
    "S2" = 4,
    "S1" = 3,
    "D4" = 2,
    "D3" = 1,
    "D2" = 1,
    "D1" = 1,
    "SMA" = 0,
    "SMK" = 0
  )
  
  return(education_scores[[education]] %||% 0)
}

#' Calculate Major Relevance Bonus
#' 
#' Calculates bonus score for major relevance
#' 
#' @param major Candidate's major
#' @param config Education configuration
#' @return Major relevance bonus score
calculate_major_relevance_bonus <- function(major, config) {
  # This would implement fuzzy matching logic for major relevance
  # For now, return a simple bonus
  if (!is.null(config$major_bonus) && is.numeric(config$major_bonus)) {
    return(config$major_bonus)
  }
  return(0)
}

#' Calculate Experience Score
#' 
#' Calculates experience score based on years of experience and industry relevance
#' 
#' @param experience Years of experience
#' @param industry Industry background
#' @param config Experience scoring configuration
#' @return Numeric experience score
calculate_experience_score <- function(experience, industry, config) {
  tryCatch({
    if (is.null(experience) || is.na(experience)) {
      return(0)
    }
    
    experience <- as.numeric(experience)
    if (is.na(experience) || experience < 0) {
      return(0)
    }
    
    if (is.null(config) || is.null(config$norma_we)) {
      return(get_default_experience_score(experience))
    }
    
    # Find matching experience range
    norma_we <- config$norma_we
    
    if (is.data.frame(norma_we)) {
      for (i in 1:nrow(norma_we)) {
        min_exp <- norma_we$Min_Exp[i]
        max_exp <- norma_we$Max_Exp[i]
        
        if (experience >= min_exp && experience < max_exp) {
          base_score <- norma_we$Score[i]
          
          # Apply industry relevance bonus
          industry_bonus <- calculate_industry_relevance_bonus(industry, config)
          return(min(base_score + industry_bonus, 5))  # Cap at maximum score
        }
      }
    }
    
    # If experience exceeds all ranges, give maximum score
    if (experience >= max(norma_we$Min_Exp, na.rm = TRUE)) {
      return(5)
    }
    
    return(0)
    
  }, error = function(e) {
    log_error("Error calculating experience score: {e$message}")
    return(0)
  })
}

#' Get Default Experience Score
#' 
#' Provides default experience scoring when configuration is unavailable
#' 
#' @param experience Years of experience
#' @return Default experience score
get_default_experience_score <- function(experience) {
  if (experience >= 5) return(5)
  if (experience >= 3) return(4)
  if (experience >= 2) return(3)
  if (experience >= 1) return(2)
  if (experience > 0) return(1)
  return(0)
}

#' Calculate Industry Relevance Bonus
#' 
#' Calculates bonus score for industry relevance
#' 
#' @param industry Candidate's industry background
#' @param config Experience configuration
#' @return Industry relevance bonus score
calculate_industry_relevance_bonus <- function(industry, config) {
  # This would implement industry matching logic
  # For now, return a simple bonus for non-empty industry
  if (!is.null(industry) && !is.na(industry) && nchar(trimws(industry)) > 0) {
    return(0.5)  # Small bonus for having industry experience
  }
  return(0)
}

#' Calculate GPA Score
#' 
#' Calculates GPA score based on education level and GPA value
#' 
#' @param gpa Candidate's GPA
#' @param education Candidate's education level
#' @param config GPA scoring configuration
#' @return Numeric GPA score
calculate_gpa_score <- function(gpa, education, config) {
  tryCatch({
    if (is.null(gpa) || is.na(gpa)) {
      return(0)
    }
    
    gpa <- as.numeric(gpa)
    if (is.na(gpa) || gpa < 0 || gpa > 4) {
      return(0)
    }
    
    if (is.null(config)) {
      return(get_default_gpa_score(gpa))
    }
    
    # Determine which GPA scale to use based on education level
    education <- toupper(trimws(education %||% ""))
    
    norma_gpa <- if (education %in% c("S3", "S2")) {
      config$norma_gpa_above_bachelor
    } else if (education == "S1") {
      config$norma_gpa_bachelor
    } else {
      config$norma_gpa_below_bachelor
    }
    
    if (is.null(norma_gpa) || !is.data.frame(norma_gpa)) {
      return(get_default_gpa_score(gpa))
    }
    
    # Find matching GPA range
    for (i in 1:nrow(norma_gpa)) {
      min_gpa <- norma_gpa$Minimum[i]
      max_gpa <- norma_gpa$Maximum[i]
      
      if (gpa >= min_gpa && gpa <= max_gpa) {
        return(norma_gpa$Score[i])
      }
    }
    
    return(0)
    
  }, error = function(e) {
    log_error("Error calculating GPA score: {e$message}")
    return(0)
  })
}

#' Get Default GPA Score
#' 
#' Provides default GPA scoring when configuration is unavailable
#' 
#' @param gpa GPA value
#' @return Default GPA score
get_default_gpa_score <- function(gpa) {
  if (gpa >= 3.5) return(5)
  if (gpa >= 3.2) return(4)
  if (gpa >= 2.9) return(3)
  if (gpa >= 2.7) return(2)
  if (gpa >= 2.5) return(1)
  return(0)
}

#' Calculate Age Score
#' 
#' Calculates age score based on age ranges
#' 
#' @param age Candidate's age
#' @param config Age scoring configuration
#' @return Numeric age score
calculate_age_score <- function(age, config) {
  tryCatch({
    if (is.null(age) || is.na(age)) {
      return(0)
    }
    
    age <- as.numeric(age)
    if (is.na(age) || age < 0 || age > 100) {
      return(0)
    }
    
    if (is.null(config) || is.null(config$norma_age)) {
      return(get_default_age_score(age))
    }
    
    norma_age <- config$norma_age
    
    if (is.data.frame(norma_age)) {
      for (i in 1:nrow(norma_age)) {
        min_age <- norma_age$Minimum_Age[i]
        max_age <- norma_age$Maximum_Age[i]
        
        if (age >= min_age && age <= max_age) {
          return(norma_age$Score[i])
        }
      }
    }
    
    return(0)
    
  }, error = function(e) {
    log_error("Error calculating age score: {e$message}")
    return(0)
  })
}

#' Get Default Age Score
#' 
#' Provides default age scoring when configuration is unavailable
#' 
#' @param age Age value
#' @return Default age score
get_default_age_score <- function(age) {
  if (age >= 22 && age <= 30) return(5)
  if (age >= 31 && age <= 35) return(4)
  if (age >= 36 && age <= 40) return(3)
  if (age >= 41 && age <= 45) return(2)
  if (age >= 18 && age <= 21) return(2)
  return(1)
}

#' Calculate TOEFL Score
#' 
#' Calculates TOEFL score based on test score ranges
#' 
#' @param toefl TOEFL test score
#' @param config TOEFL scoring configuration
#' @return Numeric TOEFL score
calculate_toefl_score <- function(toefl, config) {
  tryCatch({
    if (is.null(toefl) || is.na(toefl)) {
      return(0)
    }
    
    toefl <- as.numeric(toefl)
    if (is.na(toefl) || toefl < 0 || toefl > 677) {  # Max TOEFL score
      return(0)
    }
    
    if (is.null(config)) {
      return(get_default_toefl_score(toefl))
    }
    
    # This would implement TOEFL scoring based on configuration
    # For now, use default scoring
    return(get_default_toefl_score(toefl))
    
  }, error = function(e) {
    log_error("Error calculating TOEFL score: {e$message}")
    return(0)
  })
}

#' Get Default TOEFL Score
#' 
#' Provides default TOEFL scoring when configuration is unavailable
#' 
#' @param toefl TOEFL score
#' @return Default TOEFL score
get_default_toefl_score <- function(toefl) {
  if (toefl >= 600) return(5)
  if (toefl >= 550) return(4)
  if (toefl >= 500) return(3)
  if (toefl >= 450) return(2)
  if (toefl >= 400) return(1)
  return(0)
}

#' Calculate Weighted Score
#' 
#' Calculates final weighted score from individual component scores
#' 
#' @param education_score Education component score
#' @param experience_score Experience component score
#' @param gpa_score GPA component score
#' @param age_score Age component score
#' @param toefl_score TOEFL component score
#' @param weights Weight configuration
#' @return Final weighted score
calculate_weighted_score <- function(education_score, experience_score, gpa_score, 
                                   age_score, toefl_score, weights) {
  tryCatch({
    # Default weights if not provided
    if (is.null(weights)) {
      weights <- list(
        education = 20,
        experience = 25,
        gpa = 20,
        age = 15,
        toefl = 20
      )
    }
    
    # Ensure all scores are numeric
    education_score <- as.numeric(education_score %||% 0)
    experience_score <- as.numeric(experience_score %||% 0)
    gpa_score <- as.numeric(gpa_score %||% 0)
    age_score <- as.numeric(age_score %||% 0)
    toefl_score <- as.numeric(toefl_score %||% 0)
    
    # Calculate weighted sum
    weighted_sum <- (
      education_score * (weights$education %||% 20) +
      experience_score * (weights$experience %||% 25) +
      gpa_score * (weights$gpa %||% 20) +
      age_score * (weights$age %||% 15) +
      toefl_score * (weights$toefl %||% 20)
    )
    
    # Calculate total weight
    total_weight <- sum(unlist(weights), na.rm = TRUE)
    
    if (total_weight == 0) {
      return(0)
    }
    
    # Normalize to 0-100 scale
    final_score <- (weighted_sum / total_weight) * 100
    
    # Ensure score is within valid range
    final_score <- max(0, min(100, final_score))
    
    return(round(final_score, 2))
    
  }, error = function(e) {
    log_error("Error calculating weighted score: {e$message}")
    return(0)
  })
}

#' Determine Pass Status
#' 
#' Determines if candidate passes based on score and cutoff
#' 
#' @param score Candidate's final score
#' @param cutoff Cutoff threshold
#' @return Pass status ("PASSED" or "NOT PASSED")
determine_pass_status <- function(score, cutoff) {
  tryCatch({
    if (is.null(score) || is.na(score)) {
      return("NOT PASSED")
    }
    
    score <- as.numeric(score)
    cutoff <- as.numeric(cutoff %||% 40)
    
    if (is.na(score) || is.na(cutoff)) {
      return("NOT PASSED")
    }
    
    return(if (score >= cutoff) "PASSED" else "NOT PASSED")
    
  }, error = function(e) {
    log_error("Error determining pass status: {e$message}")
    return("NOT PASSED")
  })
}
