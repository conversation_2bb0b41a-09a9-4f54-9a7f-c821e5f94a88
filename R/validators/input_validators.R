#' Input Validation Functions
#' 
#' This module provides comprehensive input validation functions to ensure
#' data integrity and security across the application.
#' 
#' <AUTHOR> Team
#' @version 2.0.0

library(logger)
library(R6)

#' Validation Result Class
#' 
#' Represents the result of a validation operation
ValidationResult <- R6::R6Class(
  "ValidationResult",
  
  public = list(
    valid = FALSE,
    errors = list(),
    warnings = list(),
    
    #' Initialize validation result
    #' @param valid Boolean indicating if validation passed
    #' @param errors List of error messages
    #' @param warnings List of warning messages
    initialize = function(valid = TRUE, errors = list(), warnings = list()) {
      self$valid <- valid
      self$errors <- errors
      self$warnings <- warnings
    },
    
    #' Add an error to the validation result
    #' @param field Field name where error occurred
    #' @param message Error message
    add_error = function(field, message) {
      self$valid <- FALSE
      self$errors[[length(self$errors) + 1]] <- list(
        field = field,
        message = message
      )
    },
    
    #' Add a warning to the validation result
    #' @param field Field name where warning occurred
    #' @param message Warning message
    add_warning = function(field, message) {
      self$warnings[[length(self$warnings) + 1]] <- list(
        field = field,
        message = message
      )
    },
    
    #' Check if validation has errors
    #' @return Boolean indicating presence of errors
    has_errors = function() {
      length(self$errors) > 0
    },
    
    #' Check if validation has warnings
    #' @return Boolean indicating presence of warnings
    has_warnings = function() {
      length(self$warnings) > 0
    },
    
    #' Get formatted error messages
    #' @return Character vector of error messages
    get_error_messages = function() {
      sapply(self$errors, function(e) paste(e$field, ":", e$message))
    },
    
    #' Get formatted warning messages
    #' @return Character vector of warning messages
    get_warning_messages = function() {
      sapply(self$warnings, function(w) paste(w$field, ":", w$message))
    }
  )
)

#' Validate Job Vacancy ID
#' 
#' Validates that job_vacancy_id is a positive integer
#' 
#' @param job_vacancy_id Value to validate
#' @return ValidationResult object
validate_job_vacancy_id <- function(job_vacancy_id) {
  result <- ValidationResult$new()
  
  if (is.null(job_vacancy_id) || is.na(job_vacancy_id)) {
    result$add_error("job_vacancy_id", "Job vacancy ID is required")
    return(result)
  }
  
  # Try to convert to integer
  tryCatch({
    id_value <- as.integer(job_vacancy_id)
    
    if (is.na(id_value)) {
      result$add_error("job_vacancy_id", "Job vacancy ID must be a valid integer")
    } else if (id_value <= 0) {
      result$add_error("job_vacancy_id", "Job vacancy ID must be a positive integer")
    } else if (id_value > 2147483647) {  # Max 32-bit integer
      result$add_error("job_vacancy_id", "Job vacancy ID is too large")
    }
  }, error = function(e) {
    result$add_error("job_vacancy_id", "Job vacancy ID must be a valid integer")
  })
  
  return(result)
}

#' Validate Schema Name
#' 
#' Validates database schema name for security and format
#' 
#' @param schema Schema name to validate
#' @return ValidationResult object
validate_schema <- function(schema) {
  result <- ValidationResult$new()
  
  if (is.null(schema) || is.na(schema) || nchar(trimws(schema)) == 0) {
    result$add_error("schema", "Schema name is required")
    return(result)
  }
  
  schema <- trimws(schema)
  
  # Check length
  if (nchar(schema) > 63) {  # PostgreSQL identifier limit
    result$add_error("schema", "Schema name is too long (maximum 63 characters)")
  }
  
  # Check format - only alphanumeric, underscore, and hyphen allowed
  if (!grepl("^[a-zA-Z0-9_-]+$", schema)) {
    result$add_error("schema", "Schema name can only contain letters, numbers, underscores, and hyphens")
  }
  
  # Check for SQL injection patterns
  dangerous_patterns <- c(
    "drop", "delete", "insert", "update", "alter", "create", "truncate",
    "--", "/*", "*/", ";", "'", "\"", "\\", "union", "select"
  )
  
  schema_lower <- tolower(schema)
  for (pattern in dangerous_patterns) {
    if (grepl(pattern, schema_lower, fixed = TRUE)) {
      result$add_error("schema", "Schema name contains potentially dangerous characters")
      break
    }
  }
  
  # Check against known valid schemas (this would be configurable)
  valid_schemas <- c("public", "tenant1", "tenant2", "development", "staging", "production")
  if (!schema %in% valid_schemas) {
    result$add_warning("schema", "Schema name is not in the list of known valid schemas")
  }
  
  return(result)
}

#' Validate Recalculate All Parameter
#' 
#' Validates the recalculate_all boolean parameter
#' 
#' @param recalculate_all Value to validate
#' @return ValidationResult object
validate_recalculate_all <- function(recalculate_all) {
  result <- ValidationResult$new()
  
  if (is.null(recalculate_all) || is.na(recalculate_all)) {
    result$add_error("recalculate_all", "Recalculate all parameter is required")
    return(result)
  }
  
  # Try to convert to logical
  tryCatch({
    logical_value <- as.logical(recalculate_all)
    
    if (is.na(logical_value)) {
      result$add_error("recalculate_all", "Recalculate all must be true or false")
    }
  }, error = function(e) {
    result$add_error("recalculate_all", "Recalculate all must be true or false")
  })
  
  return(result)
}

#' Validate API Request Parameters
#' 
#' Comprehensive validation for common API request parameters
#' 
#' @param job_vacancy_id Job vacancy identifier
#' @param schema Database schema name
#' @param recalculate_all Boolean flag for recalculation
#' @return ValidationResult object with all validation results
validate_api_request <- function(job_vacancy_id, schema, recalculate_all) {
  result <- ValidationResult$new()
  
  # Validate each parameter
  job_id_result <- validate_job_vacancy_id(job_vacancy_id)
  schema_result <- validate_schema(schema)
  recalc_result <- validate_recalculate_all(recalculate_all)
  
  # Combine results
  result$errors <- c(job_id_result$errors, schema_result$errors, recalc_result$errors)
  result$warnings <- c(job_id_result$warnings, schema_result$warnings, recalc_result$warnings)
  result$valid <- length(result$errors) == 0
  
  return(result)
}

#' Validate Email Address
#' 
#' Validates email address format
#' 
#' @param email Email address to validate
#' @return ValidationResult object
validate_email <- function(email) {
  result <- ValidationResult$new()
  
  if (is.null(email) || is.na(email) || nchar(trimws(email)) == 0) {
    result$add_error("email", "Email address is required")
    return(result)
  }
  
  email <- trimws(email)
  
  # Basic email regex pattern
  email_pattern <- "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
  
  if (!grepl(email_pattern, email)) {
    result$add_error("email", "Invalid email address format")
  }
  
  if (nchar(email) > 254) {  # RFC 5321 limit
    result$add_error("email", "Email address is too long")
  }
  
  return(result)
}

#' Validate Numeric Range
#' 
#' Validates that a numeric value falls within a specified range
#' 
#' @param value Numeric value to validate
#' @param field_name Name of the field being validated
#' @param min_value Minimum allowed value (inclusive)
#' @param max_value Maximum allowed value (inclusive)
#' @param required Whether the value is required
#' @return ValidationResult object
validate_numeric_range <- function(value, field_name, min_value = NULL, max_value = NULL, required = TRUE) {
  result <- ValidationResult$new()
  
  if (is.null(value) || is.na(value)) {
    if (required) {
      result$add_error(field_name, paste(field_name, "is required"))
    }
    return(result)
  }
  
  # Try to convert to numeric
  tryCatch({
    numeric_value <- as.numeric(value)
    
    if (is.na(numeric_value)) {
      result$add_error(field_name, paste(field_name, "must be a valid number"))
      return(result)
    }
    
    if (!is.null(min_value) && numeric_value < min_value) {
      result$add_error(field_name, paste(field_name, "must be at least", min_value))
    }
    
    if (!is.null(max_value) && numeric_value > max_value) {
      result$add_error(field_name, paste(field_name, "must be at most", max_value))
    }
    
  }, error = function(e) {
    result$add_error(field_name, paste(field_name, "must be a valid number"))
  })
  
  return(result)
}

#' Validate String Length
#' 
#' Validates string length constraints
#' 
#' @param value String value to validate
#' @param field_name Name of the field being validated
#' @param min_length Minimum required length
#' @param max_length Maximum allowed length
#' @param required Whether the value is required
#' @return ValidationResult object
validate_string_length <- function(value, field_name, min_length = NULL, max_length = NULL, required = TRUE) {
  result <- ValidationResult$new()
  
  if (is.null(value) || is.na(value)) {
    if (required) {
      result$add_error(field_name, paste(field_name, "is required"))
    }
    return(result)
  }
  
  value <- as.character(value)
  value_length <- nchar(value)
  
  if (!is.null(min_length) && value_length < min_length) {
    result$add_error(field_name, paste(field_name, "must be at least", min_length, "characters"))
  }
  
  if (!is.null(max_length) && value_length > max_length) {
    result$add_error(field_name, paste(field_name, "must be at most", max_length, "characters"))
  }
  
  return(result)
}

#' Sanitize Input String
#' 
#' Sanitizes input strings to prevent injection attacks
#' 
#' @param input Input string to sanitize
#' @param allow_html Whether to allow HTML tags
#' @return Sanitized string
sanitize_input <- function(input, allow_html = FALSE) {
  if (is.null(input) || is.na(input)) {
    return(input)
  }
  
  input <- as.character(input)
  
  # Remove or escape dangerous characters
  if (!allow_html) {
    # Remove HTML tags
    input <- gsub("<[^>]*>", "", input)
  }
  
  # Remove null bytes
  input <- gsub("\0", "", input)
  
  # Trim whitespace
  input <- trimws(input)
  
  return(input)
}
