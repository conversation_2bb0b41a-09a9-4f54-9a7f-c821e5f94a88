#' Candidate Data Models
#' 
#' This module defines data models and DTOs (Data Transfer Objects) for
#' candidate-related data structures used throughout the application.
#' 
#' <AUTHOR> Team
#' @version 2.0.0

library(R6)
library(logger)

#' Candidate Model
#' 
#' Represents a candidate with all relevant information
Candidate <- R6::R6Class(
  "Candidate",
  
  public = list(
    user_job_vacancy_id = NULL,
    fullname = NULL,
    education = NULL,
    major = NULL,
    institution = NULL,
    gpa = NULL,
    age = NULL,
    domicile = NULL,
    expected_salary = NULL,
    experience = NULL,
    industry = NULL,
    skills_tools = NULL,
    toefl_score = NULL,
    
    #' Initialize candidate
    #' @param data List or data frame row with candidate data
    initialize = function(data = NULL) {
      if (!is.null(data)) {
        self$from_data(data)
      }
    },
    
    #' Populate candidate from data
    #' @param data List or data frame row with candidate data
    from_data = function(data) {
      if (is.data.frame(data) && nrow(data) > 0) {
        data <- as.list(data[1, ])
      }
      
      self$user_job_vacancy_id <- data$user_job_vacancy_id
      self$fullname <- data$fullname %||% data$Candidate
      self$education <- data$education %||% data$Education
      self$major <- data$major %||% data$Major
      self$institution <- data$institution %||% data$Institution
      self$gpa <- as.numeric(data$gpa %||% data$GPA)
      self$age <- as.numeric(data$age)
      self$domicile <- data$domicile %||% data$Domisili
      self$expected_salary <- as.numeric(data$expected_salary %||% data$Expected_Salary)
      self$experience <- as.numeric(data$experience %||% data$Experience)
      self$industry <- data$industry %||% data$Industry
      self$skills_tools <- data$skills_tools %||% data$Skillntools
      self$toefl_score <- as.numeric(data$toefl_score)
    },
    
    #' Convert candidate to list
    #' @return List representation of candidate
    to_list = function() {
      list(
        user_job_vacancy_id = self$user_job_vacancy_id,
        fullname = self$fullname,
        education = self$education,
        major = self$major,
        institution = self$institution,
        gpa = self$gpa,
        age = self$age,
        domicile = self$domicile,
        expected_salary = self$expected_salary,
        experience = self$experience,
        industry = self$industry,
        skills_tools = self$skills_tools,
        toefl_score = self$toefl_score
      )
    },
    
    #' Validate candidate data
    #' @return List with validation results
    validate = function() {
      errors <- list()
      warnings <- list()
      
      # Required fields
      if (is.null(self$user_job_vacancy_id) || is.na(self$user_job_vacancy_id)) {
        errors <- append(errors, "user_job_vacancy_id is required")
      }
      
      if (is.null(self$fullname) || nchar(trimws(self$fullname)) == 0) {
        errors <- append(errors, "fullname is required")
      }
      
      # Data type validations
      if (!is.null(self$gpa) && !is.na(self$gpa)) {
        if (self$gpa < 0 || self$gpa > 4) {
          warnings <- append(warnings, "GPA should be between 0 and 4")
        }
      }
      
      if (!is.null(self$age) && !is.na(self$age)) {
        if (self$age < 16 || self$age > 70) {
          warnings <- append(warnings, "Age seems unusual")
        }
      }
      
      if (!is.null(self$experience) && !is.na(self$experience)) {
        if (self$experience < 0) {
          errors <- append(errors, "Experience cannot be negative")
        }
      }
      
      if (!is.null(self$expected_salary) && !is.na(self$expected_salary)) {
        if (self$expected_salary <= 0) {
          warnings <- append(warnings, "Expected salary should be positive")
        }
      }
      
      return(list(
        valid = length(errors) == 0,
        errors = errors,
        warnings = warnings
      ))
    }
  )
)

#' Scoring Result Model
#' 
#' Represents the result of candidate scoring
ScoringResult <- R6::R6Class(
  "ScoringResult",
  
  public = list(
    user_job_vacancy_id = NULL,
    total_score = NULL,
    status = NULL,
    education_score = NULL,
    experience_score = NULL,
    gpa_score = NULL,
    age_score = NULL,
    toefl_score = NULL,
    created_at = NULL,
    
    #' Initialize scoring result
    #' @param data List with scoring result data
    initialize = function(data = NULL) {
      if (!is.null(data)) {
        self$from_data(data)
      }
      self$created_at <- Sys.time()
    },
    
    #' Populate from data
    #' @param data List with scoring result data
    from_data = function(data) {
      self$user_job_vacancy_id <- data$user_job_vacancy_id
      self$total_score <- as.numeric(data$total_score %||% data$score)
      self$status <- data$status
      self$education_score <- as.numeric(data$education_score)
      self$experience_score <- as.numeric(data$experience_score)
      self$gpa_score <- as.numeric(data$gpa_score)
      self$age_score <- as.numeric(data$age_score)
      self$toefl_score <- as.numeric(data$toefl_score)
    },
    
    #' Convert to list
    #' @return List representation
    to_list = function() {
      list(
        user_job_vacancy_id = self$user_job_vacancy_id,
        total_score = self$total_score,
        status = self$status,
        education_score = self$education_score,
        experience_score = self$experience_score,
        gpa_score = self$gpa_score,
        age_score = self$age_score,
        toefl_score = self$toefl_score,
        created_at = self$created_at
      )
    }
  )
)

#' Matching Result Model
#' 
#' Represents the result of candidate matching
MatchingResult <- R6::R6Class(
  "MatchingResult",
  
  public = list(
    user_job_vacancy_id = NULL,
    main_score = NULL,
    match_status = NULL,
    education_match = NULL,
    experience_match = NULL,
    salary_match = NULL,
    location_match = NULL,
    skills_match = NULL,
    created_at = NULL,
    
    #' Initialize matching result
    #' @param data List with matching result data
    initialize = function(data = NULL) {
      if (!is.null(data)) {
        self$from_data(data)
      }
      self$created_at <- Sys.time()
    },
    
    #' Populate from data
    #' @param data List with matching result data
    from_data = function(data) {
      self$user_job_vacancy_id <- data$user_job_vacancy_id
      self$main_score <- as.numeric(data$main_score)
      self$match_status <- data$match_status
      self$education_match <- as.numeric(data$education_match)
      self$experience_match <- as.numeric(data$experience_match)
      self$salary_match <- as.numeric(data$salary_match)
      self$location_match <- as.numeric(data$location_match)
      self$skills_match <- as.numeric(data$skills_match)
    },
    
    #' Convert to list
    #' @return List representation
    to_list = function() {
      list(
        user_job_vacancy_id = self$user_job_vacancy_id,
        main_score = self$main_score,
        match_status = self$match_status,
        education_match = self$education_match,
        experience_match = self$experience_match,
        salary_match = self$salary_match,
        location_match = self$location_match,
        skills_match = self$skills_match,
        created_at = self$created_at
      )
    }
  )
)

#' Job Configuration Model
#' 
#' Represents job scoring and matching configuration
JobConfiguration <- R6::R6Class(
  "JobConfiguration",
  
  public = list(
    job_vacancy_id = NULL,
    education_config = NULL,
    experience_config = NULL,
    gpa_config = NULL,
    age_config = NULL,
    toefl_config = NULL,
    weights = NULL,
    cutoff_value = NULL,
    matching_weights = NULL,
    matching_threshold = NULL,
    
    #' Initialize job configuration
    #' @param data List with configuration data
    initialize = function(data = NULL) {
      if (!is.null(data)) {
        self$from_data(data)
      }
    },
    
    #' Populate from data
    #' @param data List with configuration data
    from_data = function(data) {
      self$job_vacancy_id <- data$job_vacancy_id
      self$education_config <- data$Education
      self$experience_config <- data$Working_Experience
      self$gpa_config <- data$GPA
      self$age_config <- data$Age
      self$toefl_config <- data$TOEFL
      self$weights <- data$weights
      self$cutoff_value <- as.numeric(data$cutoff_value %||% data$CutoffStatus$Value)
      self$matching_weights <- data$matching_weights
      self$matching_threshold <- as.numeric(data$matching_threshold %||% 50)
    },
    
    #' Convert to list
    #' @return List representation
    to_list = function() {
      list(
        job_vacancy_id = self$job_vacancy_id,
        education_config = self$education_config,
        experience_config = self$experience_config,
        gpa_config = self$gpa_config,
        age_config = self$age_config,
        toefl_config = self$toefl_config,
        weights = self$weights,
        cutoff_value = self$cutoff_value,
        matching_weights = self$matching_weights,
        matching_threshold = self$matching_threshold
      )
    },
    
    #' Validate configuration
    #' @return List with validation results
    validate = function() {
      errors <- list()
      warnings <- list()
      
      # Check required fields
      if (is.null(self$job_vacancy_id)) {
        errors <- append(errors, "job_vacancy_id is required")
      }
      
      # Validate weights
      if (!is.null(self$weights)) {
        weight_sum <- sum(unlist(self$weights), na.rm = TRUE)
        if (abs(weight_sum - 100) > 0.01) {
          warnings <- append(warnings, paste("Weight sum is", weight_sum, "but should be 100"))
        }
      }
      
      # Validate cutoff value
      if (!is.null(self$cutoff_value)) {
        if (self$cutoff_value < 0 || self$cutoff_value > 100) {
          errors <- append(errors, "Cutoff value must be between 0 and 100")
        }
      }
      
      # Validate matching threshold
      if (!is.null(self$matching_threshold)) {
        if (self$matching_threshold < 0 || self$matching_threshold > 100) {
          errors <- append(errors, "Matching threshold must be between 0 and 100")
        }
      }
      
      return(list(
        valid = length(errors) == 0,
        errors = errors,
        warnings = warnings
      ))
    }
  )
)

#' Job Baseline Model
#' 
#' Represents job baseline requirements for matching
JobBaseline <- R6::R6Class(
  "JobBaseline",
  
  public = list(
    job_vacancy_id = NULL,
    education_level = NULL,
    job_level = NULL,
    minimum_salary = NULL,
    maximum_salary = NULL,
    domicile = NULL,
    previous_job_industry = NULL,
    tools_and_competencies = NULL,
    
    #' Initialize job baseline
    #' @param data List with baseline data
    initialize = function(data = NULL) {
      if (!is.null(data)) {
        self$from_data(data)
      }
    },
    
    #' Populate from data
    #' @param data List with baseline data
    from_data = function(data) {
      self$job_vacancy_id <- data$job_vacancy_id
      self$education_level <- data$education_level %||% data$`Education Level`
      self$job_level <- data$job_level
      self$minimum_salary <- as.numeric(data$minimum_salary)
      self$maximum_salary <- as.numeric(data$maximum_salary)
      self$domicile <- data$domicile %||% data$Domicile
      self$previous_job_industry <- data$previous_job_industry %||% data$`Previous Job Industry`
      self$tools_and_competencies <- data$tools_and_competencies %||% data$`Tools and Competencies Mastery`
    },
    
    #' Convert to list
    #' @return List representation
    to_list = function() {
      list(
        job_vacancy_id = self$job_vacancy_id,
        education_level = self$education_level,
        job_level = self$job_level,
        minimum_salary = self$minimum_salary,
        maximum_salary = self$maximum_salary,
        domicile = self$domicile,
        previous_job_industry = self$previous_job_industry,
        tools_and_competencies = self$tools_and_competencies
      )
    }
  )
)
