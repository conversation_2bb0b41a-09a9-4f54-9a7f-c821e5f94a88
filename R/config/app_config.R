#' Application Configuration Management
#' 
#' This module handles all application configuration including environment variables,
#' database connections, and application settings following the 12-factor app methodology.
#' 
#' <AUTHOR> Team
#' @version 2.0.0

library(dotenv)
library(logger)
library(R6)

#' Application Configuration Class
#' 
#' Manages application configuration with validation and type safety
AppConfig <- R6::R6Class(
  "AppConfig",
  
  private = list(
    .config = NULL,
    .validated = FALSE,
    
    validate_config = function() {
      required_vars <- c(
        "DB_NAME_READ", "DB_HOST_READ", "DB_PORT_READ", "DB_USER_READ", "DB_PASSWORD_READ",
        "DB_NAME_WRITE", "DB_HOST_WRITE", "DB_PORT_WRITE", "DB_USER_WRITE", "DB_PASSWORD_WRITE",
        "AWS_ACCESS_KEY_ID", "AWS_SECRET_ACCESS_KEY", "AWS_DEFAULT_REGION",
        "VALID_TOKEN"
      )
      
      missing_vars <- character(0)
      for (var in required_vars) {
        if (Sys.getenv(var) == "") {
          missing_vars <- c(missing_vars, var)
        }
      }
      
      if (length(missing_vars) > 0) {
        stop(paste("Missing required environment variables:", paste(missing_vars, collapse = ", ")))
      }
      
      private$.validated <- TRUE
      log_info("Configuration validation successful")
    }
  ),
  
  public = list(
    #' Initialize configuration
    initialize = function() {
      # Load environment variables
      if (file.exists(".env")) {
        load_dot_env(".env")
        log_info("Loaded environment variables from .env file")
      }
      
      # Validate configuration
      private$validate_config()
      
      # Build configuration object
      private$.config <- list(
        app = list(
          name = Sys.getenv("APP_NAME", "Candidate Scoring API"),
          version = Sys.getenv("APP_VERSION", "2.0.0"),
          environment = Sys.getenv("APP_ENV", "development"),
          port = as.integer(Sys.getenv("APP_PORT", "5656")),
          host = Sys.getenv("APP_HOST", "0.0.0.0"),
          log_level = Sys.getenv("LOG_LEVEL", "INFO")
        ),
        
        database = list(
          read = list(
            host = Sys.getenv("DB_HOST_READ"),
            port = as.integer(Sys.getenv("DB_PORT_READ")),
            dbname = Sys.getenv("DB_NAME_READ"),
            user = Sys.getenv("DB_USER_READ"),
            password = Sys.getenv("DB_PASSWORD_READ"),
            pool_size = as.integer(Sys.getenv("DB_POOL_SIZE_READ", "5")),
            timeout = as.integer(Sys.getenv("DB_TIMEOUT_READ", "30"))
          ),
          write = list(
            host = Sys.getenv("DB_HOST_WRITE"),
            port = as.integer(Sys.getenv("DB_PORT_WRITE")),
            dbname = Sys.getenv("DB_NAME_WRITE"),
            user = Sys.getenv("DB_USER_WRITE"),
            password = Sys.getenv("DB_PASSWORD_WRITE"),
            pool_size = as.integer(Sys.getenv("DB_POOL_SIZE_WRITE", "3")),
            timeout = as.integer(Sys.getenv("DB_TIMEOUT_WRITE", "30"))
          )
        ),
        
        aws = list(
          access_key_id = Sys.getenv("AWS_ACCESS_KEY_ID"),
          secret_access_key = Sys.getenv("AWS_SECRET_ACCESS_KEY"),
          region = Sys.getenv("AWS_DEFAULT_REGION"),
          s3_bucket = Sys.getenv("AWS_S3_BUCKET", "shiny-dashboard")
        ),
        
        security = list(
          valid_token = Sys.getenv("VALID_TOKEN"),
          token_expiry = as.integer(Sys.getenv("TOKEN_EXPIRY_HOURS", "24")),
          rate_limit = as.integer(Sys.getenv("RATE_LIMIT_PER_MINUTE", "100"))
        ),
        
        scoring = list(
          default_cutoff = as.numeric(Sys.getenv("DEFAULT_CUTOFF_VALUE", "40")),
          max_batch_size = as.integer(Sys.getenv("MAX_BATCH_SIZE", "1000")),
          cache_ttl = as.integer(Sys.getenv("CACHE_TTL_MINUTES", "60"))
        )
      )
      
      log_info("Application configuration initialized successfully")
    },
    
    #' Get configuration value
    #' @param path Character vector representing the path to the config value
    #' @return The configuration value
    get = function(path) {
      if (!private$.validated) {
        stop("Configuration not validated")
      }
      
      result <- private$.config
      for (key in path) {
        if (is.null(result[[key]])) {
          return(NULL)
        }
        result <- result[[key]]
      }
      result
    },
    
    #' Get all configuration
    #' @return Complete configuration list
    get_all = function() {
      if (!private$.validated) {
        stop("Configuration not validated")
      }
      private$.config
    },
    
    #' Check if running in production
    #' @return Boolean indicating if in production environment
    is_production = function() {
      self$get(c("app", "environment")) == "production"
    },
    
    #' Check if running in development
    #' @return Boolean indicating if in development environment
    is_development = function() {
      self$get(c("app", "environment")) == "development"
    }
  )
)

# Create global configuration instance
app_config <- AppConfig$new()

#' Get application configuration
#' @return AppConfig instance
get_app_config <- function() {
  app_config
}

#' Helper function to get database configuration
#' @param type Character, either "read" or "write"
#' @return Database configuration list
get_db_config <- function(type = "read") {
  if (!type %in% c("read", "write")) {
    stop("Database type must be 'read' or 'write'")
  }
  app_config$get(c("database", type))
}

#' Helper function to get AWS configuration
#' @return AWS configuration list
get_aws_config <- function() {
  app_config$get("aws")
}

#' Helper function to get security configuration
#' @return Security configuration list
get_security_config <- function() {
  app_config$get("security")
}
