#' Application Entry Point
#'
#' This script initializes and starts the refactored candidate scoring and matching API
#' with comprehensive logging, error handling, and graceful shutdown capabilities.
#'
#' <AUTHOR> Team
#' @version 2.0.0

library(plumber)
library(logger)
library(R6)

# Set application start time for uptime tracking
.GlobalEnv$.app_start_time <- Sys.time()

# Initialize logging
log_threshold(INFO)
log_appender(appender_console())

# Create logs directory if it doesn't exist
if (!dir.exists("logs")) {
  dir.create("logs", recursive = TRUE)
}

# Add file logging
log_appender(appender_file("logs/api.log"), index = 2)

log_info("Starting Candidate Scoring and Matching API v2.0.0")

# Initialize configuration and validate environment
tryCatch({
  source("R/config/app_config.R")
  config <- get_app_config()

  log_info("Configuration loaded successfully")
  log_info("Environment: {config$get(c('app', 'environment'))}")
  log_info("Port: {config$get(c('app', 'port'))}")

}, error = function(e) {
  log_error("Failed to load configuration: {e$message}")
  stop("Configuration error - cannot start application")
})

# Initialize database connections
tryCatch({
  source("R/repositories/database_manager.R")
  db_manager <- get_db_manager()

  # Test database connectivity
  health_status <- db_manager$check_health()
  if (!health_status$healthy) {
    log_error("Database health check failed")
    stop("Database connectivity error")
  }

  log_info("Database connections initialized successfully")

}, error = function(e) {
  log_error("Failed to initialize database: {e$message}")
  stop("Database initialization error - cannot start application")
})

# Source the API with error handling
tryCatch({
  pr <- plumb("api.R")
  log_info("API routes loaded successfully")

}, error = function(e) {
  log_error("Failed to load API routes: {e$message}")
  stop("API initialization error")
})

# Set up graceful shutdown handler
shutdown_handler <- function() {
  log_info("Received shutdown signal, cleaning up...")

  tryCatch({
    # Cleanup database connections
    if (exists("db_manager") && !is.null(db_manager)) {
      db_manager$finalize()
    }

    log_info("Cleanup completed successfully")

  }, error = function(e) {
    log_error("Error during cleanup: {e$message}")
  })

  log_info("Application shutdown complete")
  quit(status = 0)
}

# Register signal handlers for graceful shutdown
if (.Platform$OS.type == "unix") {
  # Unix-like systems (Linux, macOS)
  signal(2, shutdown_handler)   # SIGINT (Ctrl+C)
  signal(15, shutdown_handler)  # SIGTERM
}

# Get configuration for server startup
app_config <- get_app_config()
host <- app_config$get(c("app", "host"))
port <- app_config$get(c("app", "port"))

log_info("Starting server on {host}:{port}")

# Start the API server with error handling
tryCatch({
  pr$run(
    host = host,
    port = port,
    swagger = !app_config$is_production()  # Disable Swagger in production
  )

}, error = function(e) {
  log_error("Server startup failed: {e$message}")

  # Attempt cleanup before exit
  shutdown_handler()

}, finally = {
  log_info("Server stopped")
})




