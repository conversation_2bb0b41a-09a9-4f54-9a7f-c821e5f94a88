#' Package Installation Script
#' 
#' This script installs all required packages for the candidate scoring API
#' 
#' <AUTHOR> Team
#' @version 2.0.0

cat("Installing required packages for Candidate Scoring API v2.0.0\n")
cat("============================================================\n\n")

# List of required packages
required_packages <- c(
  # Core API and web framework
  "plumber",
  
  # Database connectivity
  "DBI",
  "RPostgres", 
  "pool",
  
  # Data manipulation
  "dplyr",
  "tidyr",
  "lubridate",
  "zoo",
  
  # JSON and configuration
  "jsonlite",
  "dotenv",
  
  # Object-oriented programming
  "R6",
  
  # Logging and utilities
  "logger",
  "digest",
  
  # Testing
  "testthat",
  
  # String manipulation (for configuration parsing)
  "stringr"
)

# Function to install packages with error handling
install_package_safely <- function(package_name) {
  tryCatch({
    if (!requireNamespace(package_name, quietly = TRUE)) {
      cat(paste("Installing", package_name, "...\n"))
      install.packages(package_name, dependencies = TRUE, repos = "https://cran.r-project.org/")
      cat(paste("✓", package_name, "installed successfully\n"))
    } else {
      cat(paste("✓", package_name, "already installed\n"))
    }
    return(TRUE)
  }, error = function(e) {
    cat(paste("✗ Failed to install", package_name, ":", e$message, "\n"))
    return(FALSE)
  })
}

# Install packages
cat("Installing packages...\n\n")

failed_packages <- character(0)
successful_packages <- character(0)

for (pkg in required_packages) {
  if (install_package_safely(pkg)) {
    successful_packages <- c(successful_packages, pkg)
  } else {
    failed_packages <- c(failed_packages, pkg)
  }
}

# Summary
cat("\n============================================================\n")
cat("Installation Summary:\n")
cat(paste("✓ Successfully installed/verified:", length(successful_packages), "packages\n"))

if (length(failed_packages) > 0) {
  cat(paste("✗ Failed to install:", length(failed_packages), "packages\n"))
  cat("Failed packages:\n")
  for (pkg in failed_packages) {
    cat(paste("  -", pkg, "\n"))
  }
  cat("\nPlease install failed packages manually:\n")
  cat(paste("install.packages(c('", paste(failed_packages, collapse = "', '"), "'))\n", sep = ""))
} else {
  cat("All packages installed successfully!\n")
}

# Verify installation
cat("\nVerifying installation...\n")
verification_failed <- character(0)

for (pkg in required_packages) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    verification_failed <- c(verification_failed, pkg)
  }
}

if (length(verification_failed) == 0) {
  cat("✓ All packages verified successfully!\n")
  cat("\nYou can now run the application with:\n")
  cat("  Rscript run.R\n\n")
  cat("Or run tests with:\n")
  cat("  Rscript scripts/run_tests.R\n\n")
} else {
  cat("✗ Verification failed for packages:\n")
  for (pkg in verification_failed) {
    cat(paste("  -", pkg, "\n"))
  }
  cat("\nPlease resolve package installation issues before proceeding.\n")
}

cat("============================================================\n")
