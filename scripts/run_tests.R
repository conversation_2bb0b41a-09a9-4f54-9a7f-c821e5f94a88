#' Test Runner Script
#' 
#' This script runs the complete test suite and provides a summary of results
#' 
#' <AUTHOR> Team
#' @version 2.0.0

library(testthat)
library(logger)

# Set up logging for test run
log_threshold(INFO)
log_appender(appender_console())

log_info("Starting test suite execution...")

# Set test environment
Sys.setenv(
  APP_ENV = "test",
  DB_NAME_READ = "test_db",
  DB_HOST_READ = "localhost",
  DB_PORT_READ = "5432",
  DB_USER_READ = "test_user",
  DB_PASSWORD_READ = "test_password",
  DB_NAME_WRITE = "test_db",
  DB_HOST_WRITE = "localhost",
  DB_PORT_WRITE = "5432",
  DB_USER_WRITE = "test_user",
  DB_PASSWORD_WRITE = "test_password",
  AWS_ACCESS_KEY_ID = "test_key",
  AWS_SECRET_ACCESS_KEY = "test_secret",
  A<PERSON>_DEFAULT_REGION = "us-east-1",
  VALID_TOKEN = "test_token_123"
)

# Function to run tests with error handling
run_test_suite <- function() {
  tryCatch({
    # Change to project root directory
    if (basename(getwd()) == "scripts") {
      setwd("..")
    }
    
    log_info("Running scoring algorithms tests...")
    test_file("tests/testthat/test-scoring-algorithms.R", reporter = "summary")
    
    log_info("Running input validation tests...")
    test_file("tests/testthat/test-input-validators.R", reporter = "summary")
    
    log_info("All tests completed successfully!")
    return(TRUE)
    
  }, error = function(e) {
    log_error("Test execution failed: {e$message}")
    return(FALSE)
  })
}

# Function to check code dependencies
check_dependencies <- function() {
  log_info("Checking required packages...")
  
  required_packages <- c(
    "plumber", "DBI", "RPostgres", "pool", "dplyr", 
    "jsonlite", "logger", "R6", "dotenv", "lubridate",
    "zoo", "testthat", "digest"
  )
  
  missing_packages <- character(0)
  
  for (pkg in required_packages) {
    if (!requireNamespace(pkg, quietly = TRUE)) {
      missing_packages <- c(missing_packages, pkg)
    }
  }
  
  if (length(missing_packages) > 0) {
    log_error("Missing required packages: {paste(missing_packages, collapse = ', ')}")
    log_info("Install missing packages with: install.packages(c('{paste(missing_packages, collapse = \"', '\")'}'))")
    return(FALSE)
  }
  
  log_info("All required packages are available")
  return(TRUE)
}

# Function to validate project structure
validate_structure <- function() {
  log_info("Validating project structure...")
  
  required_dirs <- c("R", "R/api", "R/services", "R/repositories", "R/models", "R/utils", "R/validators", "R/config", "tests", "tests/testthat")
  required_files <- c("api.R", "run.R", "README.md")
  
  missing_dirs <- character(0)
  missing_files <- character(0)
  
  for (dir in required_dirs) {
    if (!dir.exists(dir)) {
      missing_dirs <- c(missing_dirs, dir)
    }
  }
  
  for (file in required_files) {
    if (!file.exists(file)) {
      missing_files <- c(missing_files, file)
    }
  }
  
  if (length(missing_dirs) > 0) {
    log_error("Missing directories: {paste(missing_dirs, collapse = ', ')}")
    return(FALSE)
  }
  
  if (length(missing_files) > 0) {
    log_error("Missing files: {paste(missing_files, collapse = ', ')}")
    return(FALSE)
  }
  
  log_info("Project structure validation passed")
  return(TRUE)
}

# Main execution
main <- function() {
  log_info("=== Candidate Scoring API Test Suite ===")
  
  # Check dependencies
  if (!check_dependencies()) {
    log_error("Dependency check failed")
    quit(status = 1)
  }
  
  # Validate project structure
  if (!validate_structure()) {
    log_error("Project structure validation failed")
    quit(status = 1)
  }
  
  # Run tests
  test_success <- run_test_suite()
  
  if (test_success) {
    log_info("=== All tests passed! ===")
    log_info("The refactored system is ready for deployment")
    quit(status = 0)
  } else {
    log_error("=== Some tests failed ===")
    log_error("Please review the test output and fix any issues")
    quit(status = 1)
  }
}

# Execute main function
main()
