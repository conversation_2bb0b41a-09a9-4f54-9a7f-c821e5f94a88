# Library

library(dplyr)
library(tidyr)
library(readr)
library(lubridate)
library(zoo)
library(purrr)
library(stringi)
library(stringr)
library(aws.s3)
library(DBI)
library(RODBC)
library(RPostgreSQL)
library(jsonlite)
library(yaml)
library(stringdist) # add new library for major detection relevantion

# Read ENV Var
readRenviron(".env")

# AWS Setup
AWS_ACCESS_KEY_ID <- Sys.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY <- Sys.getenv("AWS_SECRET_ACCESS_KEY")
AWS_DEFAULT_REGION <- Sys.getenv("AWS_DEFAULT_REGION")

#<-- Comment for Local Debug -->#

# DB Connection

## Read
dbname_read <- Sys.getenv("DB_NAME_READ")
host_read <- Sys.getenv("DB_HOST_READ")
port_read <- Sys.getenv("DB_PORT_READ")
user_read <- Sys.getenv("DB_USER_READ")
password_read <- Sys.getenv("DB_PASSWORD_READ")

con_read <- dbConnect(
  RPostgres::Postgres(),
  dbname = dbname_read,
  host = host_read,
  port = port_read,
  user = user_read,
  password = password_read
)

# Write
dbname_write <- Sys.getenv("DB_NAME_WRITE")
host_write <- Sys.getenv("DB_HOST_WRITE")
port_write <- Sys.getenv("DB_PORT_WRITE")
user_write <- Sys.getenv("DB_USER_WRITE")
password_write <- Sys.getenv("DB_PASSWORD_WRITE")

con_write <- dbConnect(
  RPostgres::Postgres(),
  dbname = dbname_write,
  host = host_write,
  port = port_write,
  user = user_write,
  password = password_write
)

# Recruiter Input
print("Recruiter Input")


print("Base Line 2")
## Data for Schema

#<-- Comment for Local Debug -->#

## Read config from db
schema <- params$schema
job_vacancy_id <- params$job_vacancy_id

schema_sql <- paste0("SET search_path = ?schema;")
safe_schema_sql <- sqlInterpolate(con_read, schema_sql, schema = schema)
dbExecute(con_read, safe_schema_sql)

sql_filename <- 'queries/base_line.sql'
query <- paste(readLines(sql_filename), collapse = "\n")

# Prepare the statement
stmt <- dbSendQuery(con_read, query)

# Bind the parameter
dbBind(stmt, list(job_vacancy_id))

# Execute the query and fetch results
result <- dbFetch(stmt)

# Clear the result
dbClearResult(stmt)

df_base_line <- as.data.frame(result)

print(query)
print(typeof(job_vacancy_id))
print(job_vacancy_id)
print(dbIsValid(con_read))

#df_base_line <- read_csv("develop_data_input/[Match_Making]_base_line_2024_11_22.csv")

glimpse(df_base_line)

print("Recruiter Input Done")

print("Base Line Setup")

print("Base Line Setup Done")

# Recruiter Input

# Enhance working experience
translate_job_level_to_we <- function(job_level) {
  # Define the complete list of experience ranges
  we_levels <- list(
    "0-3 tahun" = c(0, 2.9999),
    "3-6 tahun" = c(3, 5.9999),
    "6-11 tahun" = c(6, 10.9999),
    "11-99 tahun" = c(11, 99),
    "0-99 tahun" = c(0, 99)  # Add this to handle "all_level_experience"
  )

  # Define a translation map for job levels
  translation_map <- list(
    "entry_level" = "0-3 tahun",
    "med_associate" = "3-6 tahun",
    "senior_lead" = "6-11 tahun",
    "principal" = "11-99 tahun",
    "all_level_experience" = "0-99 tahun" # Dynamically handle it here
  )

  # Unified logic to handle all cases
  if (is.na(job_level) || is.null(job_level)) {
    # Handle NA or null job_level
    return(list(
      full_list = we_levels,
      selected_range = NA,
      selected_range_values = NA
    ))
  } else if (job_level == "all_level_experience") {
    # Handle the special case of "all_level_experience"
    return(list(
      full_list = we_levels,
      selected_range = "0-99 tahun",
      selected_range_values = c(0, 99)
    ))
  } else if (job_level %in% names(translation_map)) {
    # Handle valid job levels
    selected_range <- translation_map[[job_level]]
    return(list(
      full_list = we_levels,
      selected_range = selected_range,
      selected_range_values = we_levels[[selected_range]]
    ))
  } else {
    # Handle unknown job levels
    return(list(
      full_list = we_levels,
      selected_range = NA,
      selected_range_values = NA
    ))
  }
}

# Example usage
we_result <- if (length(df_base_line$job_level) > 0) {
  job_level <- df_base_line$job_level[1]  # Take the first value if multiple exist
  translate_job_level_to_we(job_level)
} else {
  translate_job_level_to_we(NA)  # Handle empty case
}

base_line_we <- we_result$selected_range

### EDU

base_line_edu <-df_base_line$`Education Level`

### GPA

base_line_gpa <- NA

### Domicile

base_line_domisili <- df_base_line$Domicile

### Salary

base_line_expected_salary <- df_base_line$minimum_salary

# Define the baseline range for expected salary
base_line_expected_salary_min <- df_base_line$minimum_salary
base_line_expected_salary_max <- df_base_line$maximum_salary


### Industry - Need Fixing to handle NA from base line

# Check if df_base_line$`Previous Job Industry` is NA or NULL
if (is.na(df_base_line$`Previous Job Industry`) || is.null(df_base_line$`Previous Job Industry`)) {
  # If NA or NULL, set base_line_industry to NA
  base_line_industry <- NA
} else {
  # If not NA or NULL, process the string as a comma-separated list
  base_line_industry <- strsplit(df_base_line$`Previous Job Industry`, ", ")

  # Flatten the list to ensure it's in the expected format
  base_line_industry <- unlist(base_line_industry)

  # Convert it back to a list where each element is a single string
  base_line_industry <- as.list(base_line_industry)
}

# Output to check the result
print(base_line_industry)


# # Assuming df_base_line$`Tools and Competencies Mastery` contains a comma-separated string
# base_line_industry <- strsplit(df_base_line$`Previous Job Industry`, ", ")
#
# # Flatten the list to ensure it's in the expected format
# base_line_industry <- unlist(base_line_industry)
#
# # Convert it back to a list where each element is a single string
# base_line_industry <- as.list(base_line_industry)


### Skills n tools

# Assuming df_base_line$`Tools and Competencies Mastery` contains a comma-separated string
base_line_skillntools <- strsplit(df_base_line$`Tools and Competencies Mastery`, ", ")

# Flatten the list to ensure it's in the expected format
base_line_skillntools <- unlist(base_line_skillntools)

# Convert it back to a list where each element is a single string
base_line_skillntools <- as.list(base_line_skillntools)

##---##

#<-- Comment for Local Debug -->#

# Setup Config
print("Setup Config From DB")
## Data for Schema
sql_schema_list_prod <- paste(readLines("queries/schema_list_production.sql"), collapse = "\n")
result_schema_list_prod <- dbGetQuery(con_read, sql_schema_list_prod)

schema_list_prod <- result_schema_list_prod$scheme
schema <- params$schema

if (!(schema %in% schema_list_prod)) {
  # Close connection
  DBI::dbDisconnect(con_write)
  DBI::dbDisconnect(con_read)
  stop("Schema not found")
}

## Read config from db
schema <- params$schema
job_vacancy_id <- params$job_vacancy_id

schema_sql <- paste0("SET search_path = ?schema;")
safe_schema_sql <- sqlInterpolate(con_read, schema_sql, schema = schema)
dbExecute(con_read, safe_schema_sql)

sql_filename <- 'queries/config_by_job_vacancy_id.sql'
query <- paste(readLines(sql_filename), collapse = "\n")
safe_query <- sqlInterpolate(con_read, query, job_vacancy_id = job_vacancy_id)
result <- dbGetQuery(con_read, safe_query)

#result <- read_csv("develop_data_input/[Match_Making]_config_by_jv_id_2024_11_22.csv")

json_string <- result$matchmaking_config

#<---TMMIN_Updated--->#
# Enhanced cleaning for Python-style JSON
clean_json_string <- json_string %>%
  str_replace_all("u'", "\"") %>%        # Replace u' with "
  str_replace_all("'", "\"") %>%         # Replace single quotes with double quotes
  str_replace_all("\n", "") %>%          # Remove newlines
  str_replace_all(",\\s*\\}", "}") %>%   # Remove trailing commas before closing braces
  str_replace_all(",\\s*\\]", "]")       # Remove trailing commas before closing brackets

# Parse JSON into a list
json_data <- tryCatch({
  fromJSON(clean_json_string, simplifyVector = FALSE)
}, error = function(e) {
  message("Error parsing JSON: ", e)
  list()
})

# json_data <- tryCatch({
#   fromJSON(json_string)
# }, error = function(e) {
#   list()
# })

#json_data
#class(json_data)


print(json_data)
#cat(json_data)

# TMMIN Project - GPA

print("JSON Check")
print(str(json_data))  # Before running the function

print("JSON Check Class")
print(class(json_data$GPA$MrU_GPA$weight))

# Define the function to check existence and non-zero condition
# Improved function with better error handling and debugging

# Production-ready GPA detection function
detect_gpa_threshold <- function(data_list, var_path, threshold_value) {
  # Return structure for consistent output
  result <- list(
    success = FALSE,
    value = NA,
    error = NULL,
    message = NULL
  )

  tryCatch({
    # Validate inputs
    if (is.null(data_list) || !is.list(data_list)) {
      result$error <- "Invalid input: data_list must be a valid list"
      return(result)
    }

    if (!is.character(var_path) || length(var_path) != 1) {
      result$error <- "Invalid input: var_path must be a single string"
      return(result)
    }

    # Safe nested value extraction
    get_nested_value <- function(lst, path) {
      parts <- strsplit(path, "\\$")[[1]]
      current <- lst

      for (part in parts) {
        if (!is.list(current) || !part %in% names(current)) {
          return(NULL)
        }
        current <- current[[part]]
      }
      return(current)
    }

    # Get the value
    var_value <- get_nested_value(data_list, var_path)

    # Validate extracted value
    if (is.null(var_value)) {
      result$message <- "Path not found in data structure"
      return(result)
    }

    if (!is.numeric(var_value)) {
      result$message <- "Retrieved value is not numeric"
      return(result)
    }

    # Check the condition and set result
    if (any(var_value != 0)) {
      result$success <- TRUE
      result$value <- threshold_value
      result$message <- "Successfully found non-zero value"
    } else {
      result$message <- "All values are zero"
    }

    return(result)

  }, error = function(e) {
    result$error <- paste("Unexpected error:", e$message)
    return(result)
  })
}

# Example usage:
gpa_result <- detect_gpa_threshold(
  data_list = json_data,
  var_path = "GPA$MrU_GPA$weight",
  threshold_value = "2.9-3.2"
)

# Safe way to use the result
if (gpa_result$success) {
  base_line_gpa <- gpa_result$value
} else {
  base_line_gpa <- NA
  # Optional: Log the error/message
  if (!is.null(gpa_result$error)) {
    message("Error: ", gpa_result$error)
  } else {
    message("Info: ", gpa_result$message)
  }
}

# Test cases
test_cases <- list(
  # Test valid case
  list(
    data = json_data,
    path = "GPA$MrU_GPA$weight",
    expected = "2.9-3.2"
  ),
  # Test invalid path
  list(
    data = json_data,
    path = "Invalid$Path",
    expected = NA
  ),
  # Test NULL input
  list(
    data = NULL,
    path = "GPA$MrU_GPA$weight",
    expected = NA
  )
)

# Run tests
run_tests <- function(test_cases) {
  for (i in seq_along(test_cases)) {
    test <- test_cases[[i]]
    result <- detect_gpa_threshold(test$data, test$path, "2.9-3.2")
    cat(sprintf("\nTest %d:\n", i))
    cat("Success:", result$success, "\n")
    cat("Value:", result$value, "\n")
    cat("Error:", result$error %||% "None", "\n")
    cat("Message:", result$message %||% "None", "\n")
  }
}

print("Run the tests")
run_tests(test_cases)

print("Print GPA Base Line")
print(base_line_gpa)

# Function to Detect and Extract Major Baseline from Config
extract_base_line_major <- function(config) {
  if ("Major" %in% names(config)) {
    if ("baseline" %in% names(config$Major)) {
      return(config$Major$baseline)
    }
  }
  return(NA) # Return NA if the Major or baseline is not found
}

# Extract Major Baseline
base_line_major <- extract_base_line_major(json_data)
print(base_line_major)



#<---Pos Enhance--->#
# Improved age baseline extraction with better error handling
extract_age_baseline <- function(config) {
  tryCatch({
    if ("Age" %in% names(config) &&
        !is.null(config$Age) &&
        "baseline" %in% names(config$Age)) {
      return(config$Age$baseline)
    }
    return(NA)
  }, error = function(e) {
    warning("Error extracting age baseline: ", e$message)
    return(NA)
  })
}

# Function to Detect and Extract Age Baseline from Config
# extract_age_baseline <- function(config) {
#   if ("Age" %in% names(config)) {
#     if ("baseline" %in% names(config$Age)) {
#       return(config$Age$baseline)
#     }
#   }
#   return(NA) # Return NA if the Age or baseline is not found
# }

# Extract Age Baseline
base_line_age <- extract_age_baseline(json_data)
print(base_line_age)


#<---Pos Enhance--->#
# Function to Detect and Extract TOEFL Baseline from Config
extract_toefl_baseline <- function(config) {
  if ("TOEFL" %in% names(config)) {
    if ("baseline" %in% names(config$TOEFL)) {
      return(config$TOEFL$baseline)
    }
  }
  return(NA) # Return NA if TOEFL or baseline is not found
}

# Example Usage
base_line_toefl <- extract_toefl_baseline(json_data)
print(base_line_toefl)



print("Setup Config From DB")

### Need to Handle Config manually first

# <-- Comment for Local Debug -->#
# User Input
print("User Input")
## Data Candidate
schema <- params$schema
job_vacancy_id <- params$job_vacancy_id
recalculate_all <- as.logical(params$recalculate_all)

schema_sql <- paste0("SET search_path = ?schema;")
safe_schema_sql <- sqlInterpolate(con_read, schema_sql, schema = schema)
dbExecute(con_read, safe_schema_sql)
dbExecute(con_read, "SET work_mem='32MB'")

sql_filename <- 'queries/candidate_data_new.sql'
query <- paste(readLines(sql_filename), collapse = "\n")

if (!recalculate_all) {
  query <- paste0(
    query,
    " LEFT JOIN user_vacancy_matchmaking_results",
    " ON candidate_datas.user_job_vacancy_id = user_vacancy_matchmaking_results.user_job_vacancy_id"
  )
}

query <- paste0(
  query,
  " WHERE candidate_datas.job_vacancy_id = ?job_vacancy_id"
)

if (!recalculate_all) {
  query <- paste0(
    query,
    " AND user_vacancy_matchmaking_results.main_score IS NULL"
  )
}
# BUG - harusnya bukan score lagi -> main_score

safe_query <- sqlInterpolate(con_read, query, job_vacancy_id = job_vacancy_id)

result <- dbGetQuery(con_read, safe_query)
df_candidate <- as.data.frame(result)

#df_candidate <- read_csv("develop_data_input/[Match_Making]_candidate_data_new_2024_11_22.csv")

#View(df_candidate)
# BUG - harusnya tidak duplicate ID nya

#<-- Comment for Local Debug -->#
## Data Experience
sql_filename <- 'queries/experience_data.sql'
query <- paste(readLines(sql_filename), collapse = "\n")

if (!recalculate_all) {
  query <- paste0(
    query,
    " LEFT JOIN user_vacancy_matchmaking_results",
    " ON user_job_vacancies.id = user_vacancy_matchmaking_results.user_job_vacancy_id"
  )
}

query <- paste0(
  query,
  " WHERE user_job_vacancies.discarded_at IS NULL",
  " AND user_job_vacancies.state != 'pool'",
  " AND experiences.work_type != 'intern'",
  " AND user_job_vacancies.job_vacancy_id = ?job_vacancy_id"
)

if (!recalculate_all) {
  query <- paste0(
    query,
    " AND user_vacancy_matchmaking_results.score IS NULL"
  )
}

safe_query <- sqlInterpolate(con_read, query, job_vacancy_id = job_vacancy_id)

result <- dbGetQuery(con_read, safe_query)
df_experience <- as.data.frame(result)

#df_experience <- read_csv("develop_data_input/[Match_Making]_experience_data_2024_11_22.csv")

#View(df_experience)

## Data Campuus
df_campus <- s3readRDS(object = "df_campus.rds", bucket = "shiny-dashboard/dashboard_ops")

## Data Domisili
df_domisili <- s3readRDS(object = "df_domisili.rds", bucket = "shiny-dashboard/dashboard_ops")

### Enhance Column DF Candidate
print("Setup Candidate")

print(df_candidate)
str(df_candidate)
print(df_experience)
str(df_experience)

print("Data Experience Processing - Start")
current_date <- format(Sys.Date(), "%Y-%m-%y") #2021-05-21
current_date_time <- paste(current_date, "00:00:00")
current_date_time <- as.Date(current_date_time)
`Major (Cluster)` <- "([a-zA-Z0-9 ]+)" #config_education$norma_education[1,2]
`Major (Specific)` <- "([a-zA-Z0-9 ]+)" #config_education$norma_education[1,2]
`Industry` <- "([a-zA-Z0-9 ]+)" #config_education$norma_education[1,2]

df_experience <- df_experience |>
  mutate(
    starts_at = as.Date(starts_at),  # Convert starts_at to Date
    start_month = as.yearmon(starts_at),  # Convert to yearmon for month-level calculations
    ends_at = as.Date(ends_at),  # Convert ends_at to Date
    ends_at = coalesce(ends_at, current_date_time),  # Replace NA values in ends_at with the current date
    end_month = as.yearmon(ends_at),  # Convert to yearmon for month-level calculations
    `YoE Month` = floor(interval(starts_at, ends_at) / months(1)),  # Calculate months of experience
    `YoE Year` = round(`YoE Month` / 12, 2),  # Convert months of experience to years
    JobRoleRelevancy = ifelse(`Job Role` != "", grepl(`Major (Cluster)`, `Job Role`), FALSE),  # Replace NA with FALSE
    JobRoleRelevancy = ifelse(is.na(JobRoleRelevancy), FALSE, JobRoleRelevancy),  # Explicitly handle NA as FALSE
    JobRoleFieldRelevancy = ifelse(industry != "", grepl(`Major (Cluster)`, industry), FALSE),  # Replace NA with FALSE
    JobRoleFieldRelevancy = ifelse(is.na(JobRoleFieldRelevancy), FALSE, JobRoleFieldRelevancy)  # Explicitly handle NA as FALSE
  ) %>%
  filter(work_type != "intern")  # Filter out internships

#View(df_experience)


data_we <- data.frame(
  PositionRelevancy = c(TRUE, TRUE, TRUE, TRUE, TRUE),
  FieldRelevancy = c(TRUE, TRUE, TRUE, TRUE, TRUE),
  MajorRelevancy = c(TRUE, TRUE, TRUE, TRUE, TRUE),
  Min_Exp = c(3, 2, 1, 0.01, 0),
  Max_Exp = c(30, 3, 2, 1, 0.01),
  Score = c(5, 4, 3, 2, 1)
)

df_experience_industry <- df_experience |>
  select(`User ID`, industry) |>
  group_by(`User ID`) |>
  summarise(industry = paste(unique(na.omit(industry)), collapse = "\", \"")) |>
  mutate(industry = paste0("{\"", industry, "\"}"))

#View(df_experience_industry)

df_experience_yoe <- df_experience |>
  select(-c(Fullname, `Job Role`, `Applied Date`)) |>
  #select(`User ID`, JobRoleRelevancy, JobRoleFieldRelevancy, `YoE Year`) |>
  group_by(`User ID`) |>  #, JobRoleRelevancy, JobRoleFieldRelevancy) |>
  summarise(`YoE Year` = sum(`YoE Year`)) |>
  ungroup() #|>
  # rowwise() |>
  # mutate(
  #   ExperienceScore = ifelse(
  #     nrow(df_experience) > 0,
  #     data_we |>
  #       filter(
  #         PositionRelevancy == JobRoleRelevancy &
  #           FieldRelevancy == JobRoleFieldRelevancy &
  #           `YoE Year` >= Min_Exp &
  #           `YoE Year` < Max_Exp
  #       ) |>
  #       pull(Score),
  #     0
  #   )
  # ) |>
  # ungroup() |>
  # replace_na(list(ExperienceScore = 0))

#View(df_experience_yoe)

df_experience_yoe <- df_experience_yoe |>
  left_join(df_experience_industry, by = "User ID")

#View(df_experience_test)

print("Data Experience Processing - Done")

df_experience <- df_experience |>
  select(-c(Fullname, `Job Role`, `Applied Date`, industry))

df_experience <- df_experience_yoe |>
  left_join(df_experience, by = c("User ID", "YoE Year"))

#View(df_experience)

candidates <- df_candidate |>
  left_join(df_experience, by = "User ID")

#View(candidates_test)

#<---TMMIN_Updated--->#
candidates <- candidates |>
  select(Fullname, Degree, Institution, `Calculated YoE`, GPA, `Kota - Kab`, expect_min, industry, `Skill & Tools`, Major,
         user_job_vacancy_id,
         #<---Pos Enhance--->#
         dob) |>
  rename(Candidate = Fullname,
         Education = Degree,
         Experience = `Calculated YoE`,
         GPA = GPA,
         Domisili = `Kota - Kab`,
         #Expected_Salary_Min = expect_min,
         #Expected_Salary_Max = expect_max,
         Expected_Salary = expect_min,
         Industry = industry,
         Skillntools = `Skill & Tools`
  ) |>
  mutate(Experience = floor(Experience),
         # dob = dmy_hm(dob),  # Convert dob to datetime using lubridate #<---Pos Enhance--->#
         # age = as.numeric(difftime(Sys.Date(), dob, units = "weeks")) / 52.25,  # Calculate age in years #<---Pos Enhance--->#
         # age = floor(age) #<---Pos Enhance--->#

         #<---Pos Enhance--->#
         # First check if dob is already in POSIXct format
         dob = if(inherits(dob, "POSIXct")) {
           dob
         } else {
           # Try multiple date formats
           coalesce(
             ymd_hms(dob),
             dmy_hms(dob),
             ymd(dob),
             dmy(dob)
           )
         },
         # Calculate age only for valid dates
         age = case_when(
           !is.na(dob) ~ floor(as.numeric(difftime(Sys.Date(), dob, units = "days")) / 365.25),
           TRUE ~ NA_real_
         )
         )

# candidates <- candidates |>
#   select(Fullname, Degree, `Calculated YoE`, GPA, `Kota - Kab`, expect_min, industry, `Skill & Tools`, user_job_vacancy_id) |>
#   rename(Candidate = Fullname,
#          Education = Degree,
#          Experience = `Calculated YoE`,
#          GPA = GPA,
#          Domisili = `Kota - Kab`,
#          #Expected_Salary_Min = expect_min,
#          #Expected_Salary_Max = expect_max,
#          Expected_Salary = expect_min,
#          Industry = industry,
#          Skillntools = `Skill & Tools`
#  ) |>
#   mutate(Experience = floor(Experience))

#|>
  #select(Candidate, Education, Experience, GPA, Domisili, Expected_Salary, Industry, Skillntools, user_job_vacancy_id)
# write.csv(candidates, "candidate.csv")

candidates_exp <- candidates
#View(candidates_exp)



#<---Pos Enhance--->#

# TOEFL Score Data
print("TOEFL Score Data")
if (!is.na(base_line_toefl)) {
  schema_sql <- paste0("SET search_path = ?schema;")
  safe_schema_sql <- sqlInterpolate(con_read, schema_sql, schema = schema)
  dbExecute(con_read, safe_schema_sql)
  dbExecute(con_read, "SET work_mem='32MB'")
  sql_filename <- 'queries/toefl_scores.sql'
  query <- paste(readLines(sql_filename), collapse = "\n")

  # First add the JOIN if not recalculating all
  if (!recalculate_all) {
    query <- paste0(
      query,
      " LEFT JOIN user_vacancy_matchmaking_results uvmr",
      " ON btd.\"user_job_vacancy_id\" = uvmr.user_job_vacancy_id"
    )
  }

  # Then add WHERE conditions
  query <- paste0(
    query,
    " WHERE btd.job_vacancy_id = ?job_vacancy_id"
  )

  # Add the matchmaking filter condition in the WHERE clause
  if (!recalculate_all) {
    query <- paste0(
      query,
      " AND uvmr.main_score IS NULL"
    )
  }

  safe_query <- sqlInterpolate(con_read, query, job_vacancy_id = job_vacancy_id)
  result <- dbGetQuery(con_read, safe_query)
  df_toefl <- as.data.frame(result)
  df_toefl$"toefl_score" <- as.numeric(df_toefl$"toefl_score")
} else {
  df_toefl <- NULL
}

# # TOEFL Score Data
# print("TOEFL Score Data")
# if (!is.na(base_line_toefl)) {
#   schema_sql <- paste0("SET search_path = ?schema;")
#   safe_schema_sql <- sqlInterpolate(con_read, schema_sql, schema = schema)
#   dbExecute(con_read, safe_schema_sql)
#   dbExecute(con_read, "SET work_mem='32MB'")
#   sql_filename <- 'queries/toefl_scores.sql'
#   query <- paste(readLines(sql_filename), collapse = "\n")
#
#   # First add the JOIN if not recalculating all
#   if (!recalculate_all) {
#     query <- paste0(
#       query,
#       " LEFT JOIN user_vacancy_matchmaking_results",
#       " ON user_job_vacancy_id = user_vacancy_matchmaking_results.user_job_vacancy_id"
#     )
#   }
#
#   # Then add WHERE conditions
#   query <- paste0(
#     query,
#     " WHERE job_vacancy_id = ?job_vacancy_id"
#   )
#
#   # Add the matchmaking filter condition in the WHERE clause
#   if (!recalculate_all) {
#     query <- paste0(
#       query,
#       " AND user_vacancy_matchmaking_results.main_score IS NULL"
#     )
#   }
#
#   safe_query <- sqlInterpolate(con_read, query, job_vacancy_id = job_vacancy_id)
#   result <- dbGetQuery(con_read, safe_query)
#   df_toefl <- as.data.frame(result)
#   df_toefl$"TOEFL Score" <- as.numeric(df_toefl$"TOEFL Score")
# } else {
#   df_toefl <- NULL
# }

# # TOEFL Score Data
# print("TOEFL Score Data")
# if (!is.na(base_line_toefl)) {
#   schema_sql <- paste0("SET search_path = ?schema;")
#   safe_schema_sql <- sqlInterpolate(con_read, schema_sql, schema = schema)
#   dbExecute(con_read, safe_schema_sql)
#   dbExecute(con_read, "SET work_mem='32MB'")
#
#   sql_filename <- 'queries/toefl_scores.sql'
#   query <- paste(readLines(sql_filename), collapse = "\n")
#
#   # Add job vacancy filter
#   query <- paste0(
#     query,
#     " WHERE job_vacancy_id = ?job_vacancy_id"
#   )
#
#   # Add matchmaking filter if not recalculating all
#   if (!recalculate_all) {
#     query <- paste0(
#       query,
#       " LEFT JOIN user_vacancy_matchmaking_results",
#       " ON user_job_vacancy_id = user_vacancy_matchmaking_results.user_job_vacancy_id",
#       " AND user_vacancy_matchmaking_results.main_score IS NULL"
#     )
#   }
#
#   safe_query <- sqlInterpolate(con_read, query, job_vacancy_id = job_vacancy_id)
#   result <- dbGetQuery(con_read, safe_query)
#   df_toefl <- as.data.frame(result)
#   df_toefl$"TOEFL Score" <- as.numeric(df_toefl$"TOEFL Score")
# } else {
#   df_toefl <- NULL
# }

#df_toefl <- read_csv("develop_data_input/[Match_Making]_toefl_score_2024_11_22.csv")


# #' Fetch TOEFL scores data
# #' @param con_read Database connection for reading
# #' @param params List containing schema, job_vacancy_id, base_line_toefl, and recalculate_all
# #' @return DataFrame with TOEFL scores or NULL if base_line_toefl is NA
# #' @export
# get_toefl_data <- function(con_read, params) {
#   # Early return if base_line_toefl is NA
#   if (is.na(params$base_line_toefl)) {
#     return(NULL)
#   }
#
#   tryCatch({
#     # Set schema
#     schema_sql <- paste0("SET search_path = ?schema;")
#     safe_schema_sql <- sqlInterpolate(con_read, schema_sql, schema = params$schema)
#     dbExecute(con_read, safe_schema_sql)
#     dbExecute(con_read, "SET work_mem='32MB'")
#
#     # Read TOEFL query
#     sql_filename <- 'queries/toefl_scores.sql'
#     query <- paste(readLines(sql_filename), collapse = "\n")
#
#     # Add job_vacancy_id filter
#     query <- paste0(
#       query,
#       " AND job_vacancy_id = ?job_vacancy_id"
#     )
#
#     # Add matchmaking filter if not recalculating all
#     if (!params$recalculate_all) {
#       query <- paste0(
#         query,
#         " LEFT JOIN user_vacancy_matchmaking_results",
#         " ON user_job_vacancy_id = user_vacancy_matchmaking_results.user_job_vacancy_id",
#         " WHERE user_vacancy_matchmaking_results.main_score IS NULL"
#       )
#     }
#
#     # Execute query
#     safe_query <- sqlInterpolate(con_read, query, job_vacancy_id = params$job_vacancy_id)
#     result <- dbGetQuery(con_read, safe_query)
#
#     # Convert to data frame and ensure TOEFL Score is numeric
#     df_toefl <- as.data.frame(result)
#     df_toefl$"TOEFL Score" <- as.numeric(df_toefl$"TOEFL Score")
#
#     return(df_toefl)
#
#   }, error = function(e) {
#     warning(paste("Error fetching TOEFL data:", e$message))
#     return(NULL)
#   })
# }
#
# # Get TOEFL data independently
# df_toefl <- get_toefl_data(con_read, params)

#<---Pos Enhance--->#

# Define the function

# Improved join function with better NULL/NA handling
join_candidates_with_toefl <- function(candidates, df_toefl) {
  # Initialize toefl_score column with NA if it doesn't exist and candidates has rows
  if (!"toefl_score" %in% names(candidates)) {
    if (nrow(candidates) > 0) {
      candidates$toefl_score <- NA_real_  # Use NA_real_ for numeric consistency
    } else {
      # For empty dataframes, add the column structure without values
      candidates <- candidates |>
        mutate(toefl_score = numeric(0))
    }
  }

  # Only perform join if df_toefl has data
  if (!is.null(df_toefl) && nrow(df_toefl) > 0) {
    df_toefl_selected <- df_toefl |>
      select(user_job_vacancy_id, toefl_score)

    # Perform the join, preserving NAs
    candidates_with_toefl <- candidates |>
      left_join(df_toefl_selected, by = "user_job_vacancy_id", suffix = c("", "_new")) |>
      mutate(
        toefl_score = coalesce(toefl_score_new, toefl_score),
        toefl_score = as.numeric(toefl_score)  # Ensure numeric type
      ) |>
      select(-matches("_new$"))  # Remove any temporary columns

    return(candidates_with_toefl)
  }

  return(candidates)  # Return original with NA toefl_score column
}

# join_candidates_with_toefl <- function(candidates, df_toefl) {
#   if (!is.null(df_toefl) && nrow(df_toefl) > 0) {  # Check if df_toefl is not NULL and has rows
#     df_toefl_selected <- df_toefl |>
#       select(user_job_vacancy_id, toefl_score)
#
#     # Perform the join
#     candidates_with_toefl <- candidates |>
#       left_join(df_toefl_selected, by = "user_job_vacancy_id")
#
#     print("df joined with toefl")
#     print(candidates_with_toefl)
#
#     return(candidates_with_toefl)
#   } else {
#     print("df_toefl is NULL or empty. Skipping the join.")
#     return(candidates)  # Return the original candidates data frame
#   }
# }

# Example usage
candidates <- join_candidates_with_toefl(candidates, df_toefl)

# df_toefl_selected <- df_toefl |>
#   select(user_job_vacancy_id, Toefl_Score)
#
# # Join the two data frames using user_job_vacancy_id
# candidates_with_toefl <- candidates |>
#   left_join(df_toefl_selected, by = "user_job_vacancy_id")
#
# print("df joined with toefl")
# print(candidates_with_toefl)
#
# candidates <- candidates_with_toefl




#print(stop_code)

#<---TMMIN_Updated--->#
# Function to Detect Education Match
detect_education_match <- function(df, baseline, education_column = "Education") {
  df$edu_matched <- df[[education_column]] == baseline
  return(df)
}

# Function to Detect Major Match
detect_major_match <- function(df, config, major_column = "Major", threshold = 0.2) {
  # Check if "Major" exists in the config
  if (!"Major" %in% names(config)) {
    message("Major is not configured. Skipping major matching.")
    df$major_match <- NA
    return(df)
  }

  # Get the baseline majors from the config
  baseline_majors <- tolower(config$Major$baseline)

  # Initialize match column
  df$major_match <- sapply(tolower(df[[major_column]]), function(candidate_major) {
    # Fuzzy matching with baseline majors
    distances <- stringdist(candidate_major, baseline_majors, method = "jw")
    min_distance <- min(distances)

    # Return TRUE if the closest match is within the threshold, otherwise FALSE
    return(min_distance <= threshold)
  })

  return(df)
}

# Function to Detect Education and Major Cross-Scoring
score_education_major <- function(df, config, base_line_edu, education_column = "Education", major_column = "Major") {
  # Check if Education_Major exists in the config
  if (!"Education_Major" %in% names(config)) {
    message("Education_Major is not configured. Skipping education-major scoring.")
    df$edu_major_weight <- NA
    return(df)
  }

  # Extract conditions and ensure it's a list
  edu_major_config <- config$Education_Major
  conditions <- edu_major_config$conditions
  if (!is.list(conditions)) stop("conditions must be a list of lists.")

  # Initialize weight column
  df$edu_major_weight <- 0

  # Iterate through rows and apply conditions
  for (i in 1:nrow(df)) {
    candidate_education <- df[[education_column]][i]
    candidate_major <- df[[major_column]][i]

    # Skip if education or major is NA
    if (is.na(candidate_education) || is.na(candidate_major)) {
      next
    }

    # Handle NA or NULL values
    if (is.na(candidate_education) || is.null(candidate_education) ||
        is.na(candidate_major) || is.null(candidate_major)) {
      df$major_match[i] <- FALSE
      next
    }

    # Check conditions
    for (condition in conditions) {
      # Skip if required fields are missing
      if (is.null(condition$major_relevance) ||
          is.null(condition$candidate_education) ||
          is.null(condition$weight)) {
        next
      }

      if (candidate_education %in% condition$candidate_education) {
        major_match_value <- df$major_match[i]

        # Handle NA in major_match
        if (is.na(major_match_value)) {
          major_match_value <- FALSE
        }

        if (condition$major_relevance == "any" ||
            (condition$major_relevance == "related" && major_match_value) ||
            (condition$major_relevance == "not_related" && !major_match_value)) {
          df$edu_major_weight[i] <- condition$weight
          break
        }
      }
    }
  }
  return(df)
}


#<---Pos Enhance--->#

# Improved age scoring function with better NA and edge case handling
calculate_age_score <- function(candidates, age_column, age_data, baseline) {
  # Early return if age configuration is missing
  if (is.null(age_data) || is.null(age_data$MrC_Age)) {
    candidates$age_level <- 0
    return(candidates)
  }

  # Check if age column exists
  if (!age_column %in% names(candidates)) {
    candidates$age_level <- 0
    return(candidates)
  }

  # Extract age configuration
  age_config <- age_data$MrC_Age
  age_levels <- sapply(age_config, `[[`, "level")
  age_values <- sapply(age_config, `[[`, "value")

  # Improved bounds extraction function
  extract_bounds <- function(range) {
    # Handle "x = value" format
    if (grepl("^x = ", range)) {
      value <- as.numeric(gsub("[^0-9]", "", range))
      return(c(value, value))
    }

    # Handle "a ≤ x < b" format
    if (grepl("≤.*<", range)) {
      parts <- strsplit(range, "[≤<]")[[1]]
      parts <- trimws(parts)
      lower <- as.numeric(parts[1])
      upper <- as.numeric(gsub("[^0-9]", "", parts[3]))
      return(c(lower, upper))
    }

    return(c(NA, NA))
  }

  # Calculate age scores
  candidates$age_level <- sapply(candidates[[age_column]], function(age) {
    # Handle NA/NULL/0 cases
    if (is.na(age) || is.null(age) || age == 0) {
      return(0)
    }

    # Convert to numeric if not already
    age <- as.numeric(age)

    # Check baseline case first
    if (!is.na(baseline) && age == baseline) {
      baseline_level_idx <- which(grepl(paste0("x = ", baseline), age_levels))
      if (length(baseline_level_idx) > 0) {
        return(age_values[baseline_level_idx])
      }
    }

    # Check each range
    for (i in seq_along(age_levels)) {
      bounds <- extract_bounds(age_levels[i])
      if (any(is.na(bounds))) next

      # Special case for exact match
      if (bounds[1] == bounds[2] && age == bounds[1]) {
        return(age_values[i])
      }

      # Range check
      if (age >= bounds[1] && age < bounds[2]) {
        return(age_values[i])
      }
    }

    return(0)  # Default case
  })

  return(candidates)
}

# Function to Classify Age and Calculate Age Score
# calculate_age_score <- function(candidates, age_column, age_data, baseline) {
#   # Extract MrC_Age levels and values
#   age_levels <- sapply(age_data$MrC_Age, function(x) x$level)
#   age_values <- sapply(age_data$MrC_Age, function(x) x$value)
#
#   # Function to extract lower and upper bounds from the range
#   extract_bounds <- function(range) {
#     # Regular expressions to match the lower and upper bounds
#     bounds <- strsplit(range, "≤")[[1]]
#
#     if (length(bounds) != 2) {
#       return(c(NA, NA))  # Return NA if the range is not in the expected format
#     }
#
#     lower_bound <- as.numeric(gsub("[^0-9]", "", bounds[1]))
#     upper_bound <- as.numeric(gsub("[^0-9]", "", bounds[2]))
#
#     return(c(lower_bound, upper_bound))
#   }
#
#   # Loop over each candidate and calculate the score
#   candidates$age_level <- sapply(candidates[[age_column]], function(age) {
#     if (is.na(age)) {
#       return(NA)  # Return NA if the candidate's age is missing
#     }
#
#     for (i in seq_along(age_levels)) {
#       # Extract bounds for the current age range
#       bounds <- extract_bounds(age_levels[i])
#       lower_bound <- bounds[1]
#       upper_bound <- bounds[2]
#
#       # Check if the bounds are valid
#       if (is.na(lower_bound) || is.na(upper_bound)) {
#         next  # Skip this range if the bounds are not valid
#       }
#
#       # Check if the candidate's age falls within the range
#       if (age >= lower_bound && age < upper_bound) {
#         return(age_values[i])
#       }
#     }
#
#     # If the candidate's age equals the baseline, assign the baseline value
#     if (age == baseline) {
#       return(age_values[which(age_levels == "x = 30")])  # Assuming "x = 30" is the level for baseline
#     }
#
#     # Default if no range matches
#     return(0)
#   })
#
#   return(candidates)
# }



#<---Pos Enhance--->#

# Improved TOEFL scoring function with better NA handling
calculate_toefl_score <- function(candidates, toefl_column, toefl_data) {
  # Early return if no TOEFL configuration
  if (is.null(toefl_data) || is.null(toefl_data$MrC_TOEFL)) {
    candidates$toefl_level <- 0
    return(candidates)
  }

  # Ensure toefl_column exists
  if (!toefl_column %in% names(candidates)) {
    candidates$toefl_level <- 0
    return(candidates)
  }

  # Extract configuration
  toefl_config <- toefl_data$MrC_TOEFL
  toefl_levels <- sapply(toefl_config, `[[`, "level")
  toefl_values <- sapply(toefl_config, `[[`, "value")
  baseline <- if (!is.null(toefl_data$baseline)) toefl_data$baseline else 0

  # Improved bounds extraction
  extract_bounds <- function(range) {
    if (is.null(range) || !is.character(range)) return(c(NA, NA))

    if (grepl("≤", range)) {
      upper_bound <- as.numeric(gsub("[^0-9]", "", range))
      return(c(-Inf, upper_bound))  # Use -Inf for "≤" cases
    } else if (grepl("-", range)) {
      bounds <- strsplit(range, "-")[[1]]
      return(as.numeric(bounds))
    }
    return(c(NA, NA))
  }

  # Process each candidate's score
  candidates$toefl_level <- sapply(candidates[[toefl_column]], function(score) {
    # Handle NA/NULL cases
    if (is.na(score) || is.null(score)) {
      return(0)
    }

    # Convert to numeric if not already
    score <- as.numeric(score)

    # Check each range
    for (i in seq_along(toefl_levels)) {
      bounds <- extract_bounds(toefl_levels[i])
      if (any(is.na(bounds))) next

      if (score >= bounds[1] && score <= bounds[2]) {
        return(toefl_values[i])
      }
    }

    # Handle baseline case
    if (score <= baseline) {
      return(toefl_values[length(toefl_values)])  # Lowest score
    }

    return(0)  # Default case
  })

  return(candidates)
}

# calculate_toefl_score <- function(candidates, toefl_column, toefl_data) {
#   # Extract MrC_TOEFL levels and values dynamically
#   toefl_levels <- sapply(toefl_data$MrC_TOEFL, function(x) x$level)
#   toefl_values <- sapply(toefl_data$MrC_TOEFL, function(x) x$value)
#   baseline <- toefl_data$baseline
#
#   # Function to extract bounds from the range
#   extract_bounds <- function(range) {
#     if (grepl("≤", range)) {
#       # Handle "≤ X" format
#       upper_bound <- as.numeric(gsub("[^0-9]", "", range))
#       return(c(0, upper_bound))
#     } else if (grepl("-", range)) {
#       # Handle "X-Y" format
#       bounds <- strsplit(range, "-")[[1]]
#       lower_bound <- as.numeric(bounds[1])
#       upper_bound <- as.numeric(bounds[2])
#       return(c(lower_bound, upper_bound))
#     } else {
#       return(c(NA, NA))  # Return NA if the range format is unexpected
#     }
#   }
#
#   # Loop over each candidate and calculate the TOEFL score
#   candidates$toefl_level <- sapply(candidates[[toefl_column]], function(toefl) {
#     if (is.na(toefl)) {
#       return(0)  # Handle missing TOEFL scores
#     }
#
#     for (i in seq_along(toefl_levels)) {
#       # Extract bounds for the current level
#       bounds <- extract_bounds(toefl_levels[i])
#       lower_bound <- bounds[1]
#       upper_bound <- bounds[2]
#
#       # Check if the bounds are valid
#       if (is.na(lower_bound) || is.na(upper_bound)) {
#         next  # Skip invalid bounds
#       }
#
#       # Check if the candidate's TOEFL score falls within the range
#       if (toefl >= lower_bound && toefl <= upper_bound) {
#         return(toefl_values[i])
#       }
#     }
#
#     # If the candidate's TOEFL equals the baseline, assign the lowest value
#     if (toefl <= baseline) {
#       return(toefl_values[length(toefl_values)])  # Assign the lowest score
#     }
#
#     # Default score if no range matches
#     return(0)
#   })
#
#   return(candidates)
# }




# Assume 'candidates' is your dataframe and 'json_data' is your config
base_line_edu <- df_base_line$`Education Level`  # Dynamically fetch baseline education
candidates <- detect_education_match(candidates, base_line_edu)
candidates <- detect_major_match(candidates, json_data)
#candidates <- score_education_major(candidates, json_data, base_line_edu)
candidates <- score_education_major(candidates, json_data, base_line_edu = NULL)

#<---Pos Enhance--->#

# Wrapper function for the whole age processing
process_age_scores <- function(candidates, json_data) {
  tryCatch({
    # Extract baseline
    baseline <- extract_age_baseline(json_data)

    # Calculate scores if Age config exists
    if (!is.null(json_data$Age)) {
      candidates <- calculate_age_score(
        candidates = candidates,
        age_column = "age",
        age_data = json_data$Age,
        baseline = baseline
      )
    } else {
      candidates$age_level <- 0
    }

    return(candidates)
  }, error = function(e) {
    warning(sprintf("Error in age processing: %s", e$message))
    candidates$age_level <- 0
    return(candidates)
  })
}

# Apply the Function
candidates <- process_age_scores(candidates, json_data)
# candidates <- calculate_age_score(candidates, "age", json_data$Age, base_line_age)



#<---Pos Enhance--->#

# Wrapper function for the whole process
process_toefl_scores <- function(candidates, json_data, df_toefl) {
  tryCatch({
    # First ensure we have the toefl_score column
    candidates <- join_candidates_with_toefl(candidates, df_toefl)

    # Then calculate scores if TOEFL config exists
    if (!is.null(json_data$TOEFL)) {
      candidates <- calculate_toefl_score(
        candidates = candidates,
        toefl_column = "toefl_score",
        toefl_data = json_data$TOEFL
      )
    } else {
      candidates$toefl_level <- 0
    }

    return(candidates)
  }, error = function(e) {
    warning(sprintf("Error in TOEFL processing: %s", e$message))
    candidates$toefl_level <- 0
    return(candidates)
  })
}

# Apply the Function
candidates <- process_toefl_scores(candidates, json_data, df_toefl)

# candidates <- calculate_toefl_score(
#   candidates = candidates,
#   toefl_column = "toefl_score",
#   toefl_data = json_data$TOEFL
# )





str(candidates)
print("Setup Candidate Done")


## 1.3 Variable Input
print("Setup Variable Input")
### 1.3.1 Config General

#### config default

create_MrU_GPA <- function(base_line_gpa, default_weight = 3) {
  # If base_line_gpa is NA, set weight to 0
  if (is.na(base_line_gpa)) {
    MrU_GPA <- list(weight = 0)
  } else {
    # Otherwise, set weight to the default value or a custom one
    MrU_GPA <- list(weight = default_weight)
  }

  return(MrU_GPA)
}

create_MrU_Industry <- function(base_line_industry, default_weight = json_data$Industry$MrU_Industry$weight) {
  # If base_line_gpa is NA, set weight to 0
  if (is.na(base_line_industry)) {
    MrU_Industry <- list(weight = 0)
  } else {
    # Otherwise, set weight to the default value or a custom one
    MrU_Industry <- list(weight = default_weight)
  }

  return(MrU_Industry)
}

create_MrU_Domisil <- function(base_line_domisili, default_weight = json_data$Domisili$MrU_Domisil$weight) {
  # If base_line_gpa is NA, set weight to 0
  if (is.na(base_line_domisili)) {
    MrU_Domisil <- list(weight = 0)
  } else {
    # Otherwise, set weight to the default value or a custom one
    MrU_Domisil <- list(weight = default_weight)
  }

  return(MrU_Domisil)
}

#<---TMMIN_Updated--->#
create_MrU_Education_Major <- function(config, default_weight = 0) {
  # Check if Education_Major exists in the config
  if (!"Education_Major" %in% names(config)) {
    message("Education_Major is not configured. Returning default weight.")
    return(default_weight)  # Return default weight if not configured
  }

  # Extract the weight for MrU_Education_Major or assign default
  mrU_config <- config$Education_Major$MrU_Education_Major
  weight <- ifelse(!is.null(mrU_config$weight), mrU_config$weight, default_weight)

  return(weight)  # Return the weight for MrU_Education_Major
}

#<---Pos Enhance--->#
create_MrU_Age <- function(config, default_weight = 0) {
  # Check if Age is configured in the config
  if (!"Age" %in% names(config)) {
    message("Age is not configured. Returning default weight.")
    return(default_weight)  # Return default weight if Age is not configured
  }

  # Extract the weight for MrU_Age or assign default
  mrU_config <- config$Age$MrU_Age
  weight <- ifelse(!is.null(mrU_config$weight), mrU_config$weight, default_weight)

  return(weight)  # Return the weight for MrU_Age
}


#<---Pos Enhance--->#
create_MrU_Toefl <- function(config, default_weight = 0) {
  # Check if Toefl is configured in the config
  if (!"TOEFL" %in% names(config)) {
    message("Toefl is not configured. Returning default weight.")
    return(default_weight)  # Return default weight if Toefl is not configured
  }

  # Extract the weight for MrU_Toefl or assign default
  mrU_config <- config$TOEFL$MrU_TOEFL
  weight <- ifelse(!is.null(mrU_config$weight), mrU_config$weight, default_weight)

  return(weight)  # Return the weight for MrU_Toefl
}



#### a. MrU Criteria - Weight
print("Setup MrU")
# Define example inputs with weights
MrU_Education <- list(weight = json_data$Education$MrU_Education$weight)
MrU_WE <- list(weight = json_data$Working_Experience$MrU_WE$weight)
#MrU_GPA <- list(weight = 3)
MrU_GPA <- list(weight = json_data$GPA$MrU_GPA$weight)
MrU_Domisil <- create_MrU_Domisil(base_line_domisili)
MrU_ES <- list(weight = json_data$Expected_Salary$MrU_ES$weight)
MrU_Industry <- create_MrU_Industry(base_line_industry)
#MrU_Industry <- list(weight = json_data$Industry$MrU_Industry$weight)
MrU_Skillntools <- list(weight = json_data$Skillntools$MrU_Skillntools$weight)
MrU_Education_Major <- create_MrU_Education_Major(json_data) #<---TMMIN_Updated--->#
MrU_Age <- create_MrU_Age(json_data) #<---Pos Enhance--->#
MrU_Toefl <- create_MrU_Toefl(json_data) #<---Pos Enhance--->#

# MrU_Education <- list(weight = 1)
# MrU_WE <- list(weight = 1)
# MrU_GPA <- list(weight = 1)
# MrU_Domisil <- list(weight = 1)
# MrU_ES <- list(weight = 1)
# MrU_Industry <- list(weight = 1)
# MrU_Skillntools <- list(weight = 1)

#CutOffValue <- as.numeric(config_cutoff$Value)
CutOffValue <- 40


#### b. MrC Sub Criteria - Norma
print("Setup MrC")
# Define levels for education
education_levels <- c("SMA/SMK", "D1", "D2", "D3", "D4", "S1", "S2", "S3")

# # Define levels for working experience as numeric ranges
# we_levels <- list("0-2 tahun" = c(0, 2),
#                   "2-5 tahun" = c(2, 5),
#                   "7-12 tahun" = c(7, 12),
#                   "10-15 tahun" = c(10, 15))

we_levels <- we_result$full_list

# Define levels for GPA as numeric ranges
gpa_levels <- list("0-2.5" = c(0, 2.5),
                   "2.5-2.7" = c(2.5, 2.7),
                   "2.7-2.9" = c(2.7, 2.9),
                   "2.9-3.2" = c(2.9, 3.2),
                   "3.2-3.5" = c(3.2, 3.5),
                   "3.5-4" = c(3.5, 4))

# Function to create dynamic values for education

# Function to create dynamic values for education
create_dynamic_values_edu <- function(levels, base_line) {
  # Check if base_line is NA
  if (is.na(base_line)) {
    # Return all 0 values if baseline is NA
    return(rep(0, length(levels)))
  }

  base_index <- match(base_line, levels)

  # Handle the case when base_line is not found
  if (is.na(base_index)) {
    stop("Base line education level does not match any education levels")
  }

  values <- seq(0, length(levels) - base_index)

  # Ensure the length of values matches the length of levels
  values <- c(rep(0, base_index - 1), values)

  return(values)
}

# Function to convert a range string into a numeric range
convert_to_numeric_range <- function(range_str) {
  as.numeric(unlist(strsplit(range_str, "-"))[1:2])
}

# Enhance all_level_experience
# Function to create dynamic values for working experience levels
create_dynamic_values_we <- function(levels, base_line) {
  print(base_line)  # For debugging

  # Handle NA or null base_line
  if (is.na(base_line) || is.null(base_line)) {
    print("Base line is NA or null. Returning all zeros.")
    return(rep(0, length(levels)))
  }

  # Handle the special case when base_line is "all_level_experience"
  if (base_line == "all_level_experience") {
    return(rep(max(seq(0, length(levels) - 1)), length(levels)))
  }

  # Convert the base_line to a numeric range
  base_range <- convert_to_numeric_range(gsub(" tahun", "", base_line))

  # Find the index where the base_line falls within the levels
  base_index <- which(sapply(names(levels), function(x) {
    exp_range <- convert_to_numeric_range(gsub(" tahun", "", x))
    return(base_range[1] >= exp_range[1] && base_range[2] <= exp_range[2])
  }))

  # Handle the case when base_index is not found
  if (length(base_index) == 0) {
    stop(paste("Base line range", base_line, "does not match any experience levels"))
  }

  # Use the first match if multiple indices are found
  base_index <- base_index[1]

  # Create a sequence of values starting from 0
  values <- seq(0, length(levels) - base_index)

  # Adjust values so that all preceding levels get a value of 0
  values <- c(rep(0, base_index - 1), values)

  # Ensure the length of values matches the length of levels
  values <- values[1:length(levels)]

  return(values)
}

# Function to convert GPA ranges (assumes ranges are in the format "low-high")
convert_to_numeric_range_gpa <- function(range_str) {
  return(as.numeric(strsplit(range_str, "-")[[1]]))
}

# Function to create dynamic values for GPA
create_dynamic_values_gpa <- function(levels, base_line) {
  # Check if the base_line is NA
  if (is.na(base_line)) {
    # If base_line is NA, return a vector of zeros matching the length of levels
    return(rep(0, length(levels)))
  }

  # Convert base_line and levels to numeric ranges
  base_range <- convert_to_numeric_range_gpa(base_line)

  # Find the base index that matches the base_line range
  base_index <- which(sapply(names(levels), function(x) {
    gpa_range <- convert_to_numeric_range_gpa(x)
    return(base_range[1] >= gpa_range[1] && base_range[2] <= gpa_range[2])
  }))

  # If no match is found for base_index, return zeros
  if (length(base_index) == 0) {
    return(rep(0, length(levels)))
  }

  # Create values from the base_index
  values <- seq(0, length(levels) - base_index)

  # Ensure the length of values matches the length of levels
  values <- c(rep(0, base_index - 1), values)

  return(values)
}

#### c. MrC Dynamic Sub Criteria

# Function to convert range strings to numeric ranges
convert_to_numeric_range <- function(range_str) {
  range_str <- as.character(range_str)  # Ensure input is character
  as.numeric(unlist(strsplit(range_str, "-"))[1:2])
}

print(base_line_industry)

print("check_debug_here")

print(education_levels)
print(base_line_edu)
print(we_levels)
print(base_line_we)
#print(base_line_industry)

# Create data frames for each criterion
MrC_Education <- data_frame(level = education_levels,
                            value = create_dynamic_values_edu(education_levels, base_line_edu))

MrC_WE <- data_frame(level = names(we_levels),
                     value = create_dynamic_values_we(we_levels, base_line_we))

MrC_GPA <- data_frame(level = names(gpa_levels),
                      value = create_dynamic_values_gpa(gpa_levels, base_line_gpa))

print("or here")

### 1.3.2 Final Config JSON
print("Final Config Setup")

Education <- list(MrU_Education = MrU_Education,
                  MrC_Education = MrC_Education
)

Working_Experience <- list(MrU_WE = MrU_WE,
                           MrC_WE = MrC_WE
)

GPA <- list(MrU_GPA = MrU_GPA,
            MrC_GPA = MrC_GPA
)

Domisili <- list(MrU_Domisil = MrU_Domisil)

Expected_Salary <- list(MrU_ES = MrU_ES)

Industry <- list(MrU_Industry = MrU_Industry)

Skillntools <- list(MrU_Skillntools = MrU_Skillntools)

Education_Major <- list(MrU_Education_Major = MrU_Education_Major)

Age <- list(MrU_Age = MrU_Age) #<---Pos Enhance--->#

Toefl <- list(MrU_Toefl = MrU_Toefl) #<---Pos Enhance--->#

matchmaking_config <- list(Education = Education,
                           Working_Experience = Working_Experience,
                           GPA = GPA,
                           Domisili = Domisili,
                           Expected_Salary = Expected_Salary,
                           Industry = Industry,
                           Skillntools = Skillntools,
                           Education_Major = Education_Major, #<---TMMIN_Updated--->#
                           Age = Age, #<---Pos Enhance--->#
                           Toefl = Toefl #<---Pos Enhance--->#
)

print(Industry)
print(MrU_Industry)

print(matchmaking_config)
# match_making_config_json <- toJSON(matchmaking_config)
#match_making_config_json <- json_data <- toJSON(matchmaking_config, auto_unbox = TRUE, pretty = TRUE)
#cat(match_making_config_json)
#write_json(match_making_config_json, "match_making_config_json.json")

# Define not null Criteria
count_weight_data <- list(matchmaking_config$Education$MrU_Education,
                          matchmaking_config$Working_Experience$MrU_WE,
                          matchmaking_config$GPA$MrU_GPA,
                          matchmaking_config$Domisili$MrU_Domisil,
                          matchmaking_config$Expected_Salary$MrU_ES,
                          matchmaking_config$Industry$MrU_Industry,
                          matchmaking_config$Skillntools$MrU_Skillntools,
                          matchmaking_config$Education_Major$MrU_Education_Major, #<---TMMIN_Updated--->#
                          matchmaking_config$Age$MrU_Age, #<---Pos Enhance--->#
                          matchmaking_config$Toefl$MrU_Toefl #<---Pos Enhance--->#
)

# Count the number of elements where the value is not 0
# Enhanced code to count non-zero weights in count_weight_data
non_zero_count <- sum(sapply(count_weight_data, function(x) {
  # Check if x is NULL or NA
  if (is.null(x) || is.na(x)) {
    return(FALSE) # Treat as zero
  }

  # Extract weight if x is a list and has the 'weight' element
  if (is.list(x) && "weight" %in% names(x)) {
    weight <- x$weight
  } else {
    weight <- x
  }

  # Check if weight is non-zero (numeric and greater than 0)
  return(is.numeric(weight) && weight != 0)
}))

# non_zero_count <- sum(sapply(count_weight_data, function(x) x != 0))

# Function to detect and count variables that contain 'MrC'
count_mrc_variables <- function(config_list) {
  count <- 0
  mrc_variables <- list()

  # Iterate over each variable in the config list
  for (variable_name in names(config_list)) {
    variable <- config_list[[variable_name]]

    # Check if any key in the variable list contains 'MrC'
    if (any(grepl("MrC", names(variable)))) {
      count <- count + 1
      mrc_variables <- c(mrc_variables, variable_name)
    }
  }

  return(list(count = count, variables_with_mrc = mrc_variables))
}

# Apply the function to your matchmaking_config
result <- count_mrc_variables(matchmaking_config)

# Display the result
count_mrc <- result$count  # This will give the count of variables containing 'MrC'
result$variables_with_mrc  # This will list the names of the variables containing 'MrC'



print("Final Config Done")

# 2. Calculation

## 2.1 MrU

### 2.1.1 Scale MrU

print("Scale MrU")
# Example input data
weight_data <- list(weight_education = Education$MrU_Education$weight,
                    weight_we = Working_Experience$MrU_WE$weight,
                    weight_gpa = GPA$MrU_GPA$weight,
                    weight_domisili = Domisili$MrU_Domisil$weight,
                    weight_es = Expected_Salary$MrU_ES$weight,
                    weight_industry = Industry$MrU_Industry$weight,
                    weight_skill = Skillntools$MrU_Skillntools$weight,
                    weight_education_major = Education_Major$MrU_Education_Major, #<---TMMIN_Updated--->#
                    weight_age = Age$MrU_Age, #<---Pos Enhance--->#
                    weight_toefl = Toefl$MrU_Toefl #<---Pos Enhance--->#
)

print(weight_data)

# Filter out non-zero values and get their names
non_zero_values <- unlist(weight_data)[unlist(weight_data) != 0]
non_zero_names <- names(non_zero_values)

# Sum the non-zero values
sum_non_zero <- sum(non_zero_values)

# Determine the scaling factor to make the sum equal to 100
scaling_factor <- 100 / sum_non_zero

# Scale the non-zero values to make their sum 100
scaled_values <- non_zero_values * scaling_factor

# Convert scaled values back to a list with appropriate names
scaled_values_list <- setNames(as.list(scaled_values), non_zero_names)

print("Scaled value")

# Print the scaled values list
print(scaled_values_list)

print("Scale Mru Done")

### 2.1.2 Cal MrU

print("Cal MrU")
print("Test New Code")
print("Start New Function")

# BUG - Need to be can handle NA or NULL data
# Function to convert range strings to numeric ranges
convert_to_numeric_range <- function(range_str) {
  range_str <- as.character(range_str)  # Ensure input is character
  as.numeric(unlist(strsplit(range_str, "-"))[1:2])
}

# Helper function to determine if a candidate meets or exceeds the baseline for education

# ini yg paling baru
# Function to score candidate's education against the baseline

score_education <- function(candidate_edu, base_line, scaled_score) {
  match_items <- list()
  unmatch_items <- list()

  # If baseline is NA, return score 0 and empty match/unmatch items
  if (is.na(base_line)) {
    return(list(score = 0, education = list(match_item = list(), unmatch_item = list())))
  }

  # Check if the candidate's education is NA or not in education levels
  if (is.na(candidate_edu) || !(candidate_edu %in% education_levels)) {
    match_items <- list(list(name = base_line, matched = FALSE))
    if (!is.na(candidate_edu)) {
      unmatch_items <- list(list(name = candidate_edu, matched = FALSE))
    }
    return(list(score = 0, education = list(match_item = match_items, unmatch_item = unmatch_items)))
  }

  # Remaining logic for comparing candidate's education with the baseline
  edu_index <- match(candidate_edu, education_levels)
  base_index <- match(base_line, education_levels)

  if (edu_index < base_index) {
    match_items <- list(list(name = base_line, matched = FALSE))
    unmatch_items <- list(list(name = candidate_edu, matched = FALSE))
    return(list(score = 0, education = list(match_item = match_items, unmatch_item = unmatch_items)))
  } else if (edu_index == base_index) {
    match_items <- list(list(name = base_line, matched = TRUE))
    return(list(score = scaled_score, education = list(match_item = match_items, unmatch_item = unmatch_items)))
  } else {
    match_items <- list(list(name = candidate_edu, matched = TRUE))
    return(list(score = scaled_score, education = list(match_item = match_items, unmatch_item = unmatch_items)))
  }
}


# Enhance all_level_experience
score_working_experience <- function(candidate_we, base_line, scaled_score) {
  match_items <- list()
  unmatch_items <- list()

  if (is.na(base_line)) {
    return(list(score = 0, working_experience = list(match_item = list(), unmatch_item = list())))
  }

  # Convert base line to numeric range for comparison
  base_range <- convert_to_numeric_range(gsub(" tahun", "", base_line))

  # Special case for "all_level_experience" - MrU only
  if (base_line == "0-99 tahun") {
    match_items <- list(list(name = "all levels", matched = TRUE))
    return(list(score = scaled_score, working_experience = list(match_item = match_items, unmatch_item = unmatch_items)))
  }

  # Determine the candidate's level
  candidate_level <- names(we_levels)[sapply(we_levels, function(x) {
    candidate_we >= x[1] && candidate_we <= x[2]
  })]

  if (length(candidate_level) == 0) {
    match_items <- list(list(name = base_line, matched = FALSE))
    unmatch_items <- list(list(name = candidate_we, matched = FALSE))
    return(list(score = 0, working_experience = list(match_item = match_items, unmatch_item = unmatch_items)))
  }

  candidate_range <- convert_to_numeric_range(gsub(" tahun", "", candidate_level))

  # Adjusted condition: Candidate experience should be >= base range to score MrC
  if (candidate_range[1] < base_range[1]) {
    match_items <- list(list(name = base_line, matched = FALSE))
    unmatch_items <- list(list(name = candidate_we, matched = FALSE))
    return(list(score = 0, working_experience = list(match_item = match_items, unmatch_item = unmatch_items)))
  }

  # Return the score and match items
  match_items <- list(list(name = candidate_we, matched = TRUE))
  return(list(score = scaled_score, working_experience = list(match_item = match_items, unmatch_item = unmatch_items)))
}

# Test GPA Fixing Match Making Tab
# Helper function to determine if a candidate meets or exceeds the baseline for GPA

score_gpa <- function(candidate_gpa, base_line_gpa, scaled_score) {

  # Debug prints
  print(paste("Candidate GPA:", candidate_gpa))
  print(paste("Baseline GPA:", base_line_gpa))

  # Initialize empty lists for matches and unmatches
  match_items <- list()
  unmatch_items <- list()

  # Function to validate GPA value
  is_valid_gpa <- function(gpa) {
    if (is.null(gpa) || is.na(gpa)) {
      return(FALSE)
    }

    # For numeric (candidate) GPA
    if (is.numeric(gpa)) {
      return(gpa >= 0 && gpa <= 4.0)
    }

    # For range (baseline) GPA
    if (is.character(gpa)) {
      gpa_parts <- strsplit(gpa, "-")[[1]]
      if (length(gpa_parts) != 2) return(FALSE)

      gpa_range <- try(as.numeric(gpa_parts), silent = TRUE)
      if (inherits(gpa_range, "try-error") || length(gpa_range) != 2) {
        return(FALSE)
      }

      # Check if range values are valid GPAs
      return(all(gpa_range >= 0 & gpa_range <= 4.0))
    }
    return(FALSE)
  }

  # Parse GPA range into numeric vector
  parse_gpa_range <- function(gpa) {
    as.numeric(strsplit(gpa, "-")[[1]])
  }

  # Validate baseline GPA
  if (!is_valid_gpa(base_line_gpa)) {
    return(list(
      score = 0,
      gpa = list(
        match_item = list(),
        unmatch_item = list(list(
          name = "Invalid baseline GPA",
          matched = FALSE
        ))
      )
    ))
  }

  # Validate candidate GPA
  if (!is_valid_gpa(candidate_gpa)) {
    return(list(
      score = 0,
      gpa = list(
        match_item = list(),
        unmatch_item = list(list(
          name = "Invalid candidate GPA",
          matched = FALSE
        ))
      )
    ))
  }

  # Convert GPAs to appropriate numeric values
  base_range <- if (is.character(base_line_gpa)) parse_gpa_range(base_line_gpa) else base_line_gpa
  candidate_value <- as.numeric(candidate_gpa)

  # Evaluate if candidate meets requirements
  if (candidate_value >= base_range[1] && candidate_value <= 4.0) {
    match_items <- list(list(
      name = as.character(candidate_value),
      matched = TRUE
    ))
    score <- scaled_score
  } else {
    unmatch_items <- list(list(
      name = as.character(candidate_value),
      matched = FALSE
    ))
    score <- 0
  }

  # Return structured output
  return(list(
    score = score,
    gpa = list(
      match_item = match_items,
      unmatch_item = unmatch_items
    )
  ))
}



# 21 October Add Code
score_domisili <- function(candidate_domisili, base_line, scaled_score) {
  # Debug prints
  print(sprintf("Candidate domisili: %s", candidate_domisili))
  print(sprintf("Base line: %s", base_line))
  print(sprintf("Scaled score: %s", scaled_score))

  # Initialize empty lists
  match_items <- list()
  unmatch_items <- list()

  # Handle NULL/NA cases for scaled_score
  if (is.null(scaled_score) || is.na(scaled_score)) {
    return(list(
      score = 0,
      domicile = list(match_item = list(), unmatch_item = list())
    ))
  }

  # Handle NULL/NA cases for candidate_domisili
  if (is.null(candidate_domisili) || is.na(candidate_domisili)) {
    if (!is.null(base_line) && !is.na(base_line)) {
      match_items <- list(list(name = base_line, matched = FALSE))
    }
    return(list(
      score = 0,
      domicile = list(match_item = match_items, unmatch_item = unmatch_items)
    ))
  }

  # Handle NULL/NA cases for base_line
  if (is.null(base_line) || is.na(base_line)) {
    return(list(
      score = 0,
      domicile = list(match_item = list(), unmatch_item = list())
    ))
  }

  # Compare values
  if (tolower(trimws(candidate_domisili)) == tolower(trimws(base_line))) {
    match_items <- list(list(name = base_line, matched = TRUE))
    score <- scaled_score
  } else {
    match_items <- list(list(name = base_line, matched = FALSE))
    unmatch_items <- list(list(name = candidate_domisili, matched = FALSE))
    score <- 0
  }

  return(list(
    score = score,
    domicile = list(match_item = match_items, unmatch_item = unmatch_items)
  ))
}

# # Helper function to score Expected Salary

score_expected_salary <- function(candidate_salary, base_line_min, base_line_max, scaled_score) {
  match_items <- list()
  unmatch_items <- list()

  # If the candidate salary is missing, baseline is unmatched
  if (is.na(candidate_salary)) {
    match_items <- list(list(name = paste0(formatC(base_line_min, format = "f", big.mark = "", digits = 0), " - ",
                                           formatC(base_line_max, format = "f", big.mark = "", digits = 0)),
                             matched = FALSE))
    return(list(score = 0, expected_salary = list(match_item = match_items, unmatch_item = unmatch_items)))
  }

  candidate_salary <- as.numeric(candidate_salary)

  # If the candidate salary is greater than the maximum of the baseline
  if (candidate_salary > base_line_max) {
    # No score, add the candidate salary to unmatch_item, and baseline range to match_item
    match_items <- list(list(name = paste0(formatC(base_line_min, format = "f", big.mark = "", digits = 0), " - ",
                                           formatC(base_line_max, format = "f", big.mark = "", digits = 0)),
                             matched = FALSE))
    unmatch_items <- list(list(name = candidate_salary, matched = FALSE))
    return(list(score = 0, expected_salary = list(match_item = match_items, unmatch_item = unmatch_items)))
  }

  # If the candidate salary is less than or equal to the baseline maximum, give score
  if (candidate_salary <= base_line_max) {
    # Score based on the candidate salary being within the range
    match_items <- list(list(name = candidate_salary, matched = TRUE))
    return(list(score = scaled_score, expected_salary = list(match_item = match_items, unmatch_item = unmatch_items)))
  }
}



# Helper function to score industry

score_industry <- function(candidate_industries_str, base_line, scaled_score) {
  match_items <- list()
  unmatch_items <- list()

  # If baseline is NA, return score 0 and empty match/unmatch items
  if (is.na(base_line)) {
    return(list(score = 0, industry = list(match_item = list(), unmatch_item = list())))
  }

  # If candidate industries are missing, return baseline as unmatched
  if (is.null(candidate_industries_str) || is.na(candidate_industries_str) ||
      candidate_industries_str == "{\"\"}" || trimws(candidate_industries_str) == "") {
    match_items <- lapply(base_line, function(x) list(name = x, matched = FALSE))
    return(list(score = 0, industry = list(match_item = match_items, unmatch_item = list())))
  }

  # Remaining logic for industry comparison
  candidate_industries <- gsub("[{}\"]", "", candidate_industries_str)
  candidate_industries <- strsplit(candidate_industries, ",\\s*")[[1]]

  candidate_industries_lower <- tolower(candidate_industries)
  base_line_lower <- tolower(base_line)

  matches_lower <- intersect(candidate_industries_lower, base_line_lower)

  match_items <- lapply(matches_lower, function(match) {
    original_base_name <- base_line[which(tolower(base_line) == match)[1]]
    list(name = original_base_name, matched = TRUE)
  })

  unmatched_baseline_lower <- setdiff(base_line_lower, matches_lower)
  match_items <- c(match_items, lapply(unmatched_baseline_lower, function(item) {
    original_name <- base_line[which(tolower(base_line) == item)[1]]
    list(name = original_name, matched = FALSE)
  }))

  unmatched_candidate_lower <- setdiff(candidate_industries_lower, matches_lower)
  unmatch_items <- lapply(unmatched_candidate_lower, function(item) {
    original_name <- candidate_industries[which(tolower(candidate_industries) == item)[1]]
    list(name = original_name, matched = FALSE)
  })

  score <- if (length(matches_lower) > 0) scaled_score else 0

  return(list(
    score = score,
    industry = list(match_item = match_items, unmatch_item = unmatch_items)
  ))
}

score_skillntools <- function(candidate_skills, base_line, scaled_score) {
  match_items <- list()
  unmatch_items <- list()

  # Store original versions of candidate and baseline skills
  original_candidate_skills <- unlist(strsplit(gsub("[{}\"]", "", candidate_skills), ",\\s*"))
  original_base_line <- base_line

  if (is.na(candidate_skills)) {
    match_items <- lapply(original_base_line, function(skill) list(name = skill, matched = FALSE))
    return(list(score = 0, skill_and_tools = list(match_item = match_items, unmatch_item = unmatch_items)))
  }

  # Convert to lowercase for matching purposes
  candidate_skills <- tolower(unlist(strsplit(gsub("[{}\"]", "", candidate_skills), ",\\s*")))
  base_line <- tolower(unlist(base_line))

  # Check if all baseline skills are present in the candidate's skills
  all_matched <- all(base_line %in% candidate_skills)

  # All baseline skills go to match_items (with original casing)
  match_items <- lapply(seq_along(base_line), function(i) {
    skill <- base_line[i]
    original_name <- original_base_line[i]
    list(name = original_name, matched = skill %in% candidate_skills)
  })

  # Non-matching candidate skills go to unmatch_items (with original casing)
  non_matches_candidate <- setdiff(candidate_skills, base_line)
  unmatch_items <- lapply(non_matches_candidate, function(skill) {
    original_name <- original_candidate_skills[which(tolower(original_candidate_skills) == skill)[1]]
    list(name = original_name, matched = FALSE)
  })

  # If not all baseline skills are matched, return score of 0
  if (!all_matched) {
    return(list(
      score = 0,
      skill_and_tools = list(match_item = match_items, unmatch_item = unmatch_items)
    ))
  }

  # Score based on the scaled score if all baseline skills are matched
  score <- scaled_score

  return(list(
    score = score,
    skill_and_tools = list(match_item = match_items, unmatch_item = unmatch_items)
  ))
}


# Function to Detect Major Match and Unmatch
detect_major_match_items <- function(candidate_major, baseline_majors) {
  # Initialize return structure
  match_items <- list()
  unmatch_items <- list()

  # Case 1: If baseline_majors is NA
  if (length(baseline_majors) == 1 && is.na(baseline_majors)) {
    # If candidate has a value, put it in unmatch
    if (!is.null(candidate_major) && !is.na(candidate_major) && trimws(candidate_major) != "") {
      unmatch_items <- list(list(name = candidate_major, matched = FALSE))
    }
    match_items <- list(list(name = NA, matched = FALSE))
    return(list(
      match_item = match_items,
      unmatch_item = unmatch_items
    ))
  }

  # Case 2: If baseline is NULL or empty
  if (is.null(baseline_majors) || length(baseline_majors) == 0) {
    if (!is.null(candidate_major) && !is.na(candidate_major) && trimws(candidate_major) != "") {
      unmatch_items <- list(list(name = candidate_major, matched = FALSE))
    }
    return(list(
      match_item = list(),
      unmatch_item = unmatch_items
    ))
  }

  # Case 3: If candidate major is NULL, NA, or empty
  if (is.null(candidate_major) ||
      length(candidate_major) == 0 ||
      (length(candidate_major) == 1 && (is.na(candidate_major) || trimws(candidate_major) == ""))) {
    # Put all baseline majors in match items with matched = FALSE
    match_items <- lapply(baseline_majors, function(major) list(name = major, matched = FALSE))
    return(list(
      match_item = match_items,
      unmatch_item = list()
    ))
  }

  # Remove NA values from baseline_majors
  baseline_majors <- baseline_majors[!is.na(baseline_majors)]
  if (length(baseline_majors) == 0) {
    unmatch_items <- list(list(name = candidate_major, matched = FALSE))
    return(list(
      match_item = list(list(name = NA, matched = FALSE)),
      unmatch_item = unmatch_items
    ))
  }

  # Perform fuzzy matching
  candidate_major_lower <- tolower(candidate_major)
  baseline_majors_lower <- tolower(baseline_majors)

  # Calculate string distances
  distances <- stringdist(candidate_major_lower, baseline_majors_lower, method = "jw")

  # Handle invalid distance calculation
  if (is.null(distances) || all(is.na(distances))) {
    unmatch_items <- list(list(name = candidate_major, matched = FALSE))
    match_items <- lapply(baseline_majors, function(major) list(name = major, matched = FALSE))
    return(list(
      match_item = match_items,
      unmatch_item = unmatch_items
    ))
  }

  # Find best match
  min_distance <- min(distances, na.rm = TRUE)
  matched_index <- which(distances == min_distance)[1]

  # If we have a good match (distance <= 0.2)
  if (!is.na(min_distance) && min_distance <= 0.2) {
    # Create match items list with the matched baseline major
    match_items <- lapply(seq_along(baseline_majors), function(i) {
      list(name = baseline_majors[i], matched = (i == matched_index))
    })
  } else {
    # No good match found
    # Put all baseline majors in match items with matched = FALSE
    match_items <- lapply(baseline_majors, function(major) list(name = major, matched = FALSE))
    # Put candidate major in unmatch items
    unmatch_items <- list(list(name = candidate_major, matched = FALSE))
  }

  return(list(
    match_item = match_items,
    unmatch_item = unmatch_items
  ))
}


#<---Pos Enhance--->#

score_age <- function(df, config, base_line_age, age_column = "age", weight_age = NULL) {
  # Level to score mapping
  level_score_map <- c(
    "5" = 1.0, "4" = 0.8, "3" = 0.6, "2" = 0.4, "1" = 0.2, "0" = 0.0
  )

  # Check if Age configuration exists in the config
  if (!"Age" %in% names(config)) {
    message("Age is not configured. Skipping age scoring.")
    return(list(
      age_weight = 0,
      match_item = list(list(name = "Age not configured", matched = FALSE)),
      unmatch_item = list()
    ))
  }

  # Get candidate age
  candidate_age <- df[[age_column]]

  # Handle NA cases
  if (is.na(candidate_age) || is.na(base_line_age)) {
    return(list(
      age_weight = 0,
      match_item = list(),
      unmatch_item = list(list(name = "Invalid age data"))
    ))
  }

  # Extract age configuration
  age_config <- config$Age
  age_criteria <- age_config$MrC_Age

  # Initialize variables
  max_level <- 0
  is_matched <- FALSE
  match_item <- list()
  unmatch_item <- list()

  # Check each age criterion
  for (criterion in age_criteria) {
    level <- criterion$level
    value <- criterion$value

    # Parse the age range
    range_str <- gsub("x", "", level)
    range_str <- gsub("≤", "", range_str)
    range_str <- gsub("=", "", range_str)
    range_parts <- strsplit(range_str, "<")[[1]]
    range_parts <- as.numeric(gsub(" ", "", range_parts))

    if (length(range_parts) == 2) {
      min_age <- range_parts[1]
      max_age <- range_parts[2]
      if (candidate_age >= min_age && candidate_age < max_age) {
        is_matched <- TRUE
        max_level <- max(max_level, value)
      }
    } else if (length(range_parts) == 1) {
      exact_age <- range_parts[1]
      if (candidate_age == exact_age) {
        is_matched <- TRUE
        max_level <- max(max_level, value)
      }
    }
  }

  # Create match or unmatch item based on result
  if (is_matched) {
    match_item <- list(list(name = as.character(candidate_age), matched = TRUE))
    unmatch_item <- list()
  } else {
    match_item <- list()
    unmatch_item <- list(list(name = as.character(candidate_age)))
  }

  # Calculate score
  level_score <- level_score_map[as.character(max_level)]
  if (is.na(level_score)) level_score <- 0
  final_score <- level_score * weight_age
  if (is.na(final_score)) final_score <- 0

  return(list(
    age_weight = final_score,
    match_item = match_item,
    unmatch_item = unmatch_item
  ))
}

# score_age <- function(df, config, base_line_age, age_column = "age", weight_age = NULL) {
#   # Level to score mapping
#   level_score_map <- c(
#     "5" = 1.0,    # 100%
#     "4" = 0.8,    # 80%
#     "3" = 0.6,    # 60%
#     "2" = 0.4,    # 40%
#     "1" = 0.2,    # 20%
#     "0" = 0.0     # 0%
#   )
#
#   # Check if Age configuration exists in the config
#   if (!"Age" %in% names(config)) {
#     message("Age is not configured. Skipping age scoring.")
#     return(list(
#       age_weight = 0,
#       match_item = character(0),
#       unmatch_item = character(0)
#     ))
#   }
#
#   # Extract age configuration
#   age_config <- config$Age
#   age_criteria <- age_config$MrC_Age
#
#   # Get candidate age
#   candidate_age <- df[[age_column]]
#
#   # Initialize results
#   match_items <- character(0)
#   unmatch_items <- character(0)
#   max_level <- 0
#
#   # Skip processing if age is NA
#   if (is.na(candidate_age) || is.na(base_line_age)) {
#     return(list(
#       age_weight = 0,
#       match_item = match_items,
#       unmatch_item = unmatch_items
#     ))
#   }
#
#   # Check each age criterion
#   for (criterion in age_criteria) {
#     level <- criterion$level
#     value <- criterion$value
#
#     # Parse the age range from the level string
#     range_str <- gsub("x", "", level)  # Remove 'x' from string
#     range_str <- gsub("≤", "", range_str)  # Remove '≤' symbol
#     range_str <- gsub("=", "", range_str)  # Remove '=' symbol
#     range_parts <- strsplit(range_str, "<")[[1]]
#     range_parts <- as.numeric(gsub(" ", "", range_parts))
#
#     if (length(range_parts) == 2) {
#       # Case: "21 ≤ x < 24"
#       min_age <- range_parts[1]
#       max_age <- range_parts[2]
#
#       if (candidate_age >= min_age && candidate_age < max_age) {
#         match_items <- c(match_items, level)
#         if (value > max_level) max_level <- value
#       } else {
#         unmatch_items <- c(unmatch_items, level)
#       }
#     } else if (length(range_parts) == 1) {
#       # Case: "x = 30"
#       exact_age <- range_parts[1]
#
#       if (candidate_age == exact_age) {
#         match_items <- c(match_items, level)
#         if (value > max_level) max_level <- value
#       } else {
#         unmatch_items <- c(unmatch_items, level)
#       }
#     }
#   }
#
#   # Convert level to score and apply weight
#   level_score <- level_score_map[as.character(max_level)]
#   if (is.na(level_score)) level_score <- 0
#
#   # Calculate final weighted score
#   final_score <- level_score * weight_age
#   if (is.na(final_score)) final_score <- 0
#
#   # Return results as a list
#   return(list(
#     age_weight = final_score,
#     match_item = match_items,
#     unmatch_item = unmatch_items
#   ))
# }


#<---Pos Enhance--->#

score_toefl <- function(df, config, base_line_toefl, toefl_column = "toefl_score", weight_toefl = NULL) {
  # Level to score mapping
  level_score_map <- c(
    "5" = 1.0, "4" = 0.8, "3" = 0.6, "2" = 0.4, "1" = 0.2, "0" = 0.0
  )

  # Check if TOEFL configuration exists in the config
  if (!"TOEFL" %in% names(config)) {
    message("TOEFL is not configured. Skipping TOEFL scoring.")
    return(list(
      toefl_weight = 0,
      match_item = list(list(name = "TOEFL not configured", matched = FALSE)),
      unmatch_item = list()
    ))
  }

  # Get candidate TOEFL score
  candidate_toefl <- df[[toefl_column]]

  # Handle NA cases
  if (is.na(candidate_toefl) || is.na(base_line_toefl)) {
    return(list(
      toefl_weight = 0,
      match_item = list(),
      unmatch_item = list(list(name = "Invalid TOEFL score"))
    ))
  }

  # Extract TOEFL configuration
  toefl_config <- config$TOEFL
  toefl_criteria <- toefl_config$MrC_TOEFL

  # Initialize variables
  max_level <- 0
  is_matched <- FALSE
  match_item <- list()
  unmatch_item <- list()

  # Check each TOEFL criterion
  for (criterion in toefl_criteria) {
    level <- criterion$level
    value <- criterion$value

    if (grepl("-", level)) {
      range_parts <- as.numeric(strsplit(level, "-")[[1]])
      min_score <- range_parts[1]
      max_score <- range_parts[2]
      if (candidate_toefl >= min_score && candidate_toefl <= max_score) {
        is_matched <- TRUE
        max_level <- max(max_level, value)
      }
    } else if (grepl("≤", level)) {
      max_score <- as.numeric(gsub("≤ ", "", level))
      if (candidate_toefl <= max_score) {
        is_matched <- TRUE
        max_level <- max(max_level, value)
      }
    }
  }

  # Create match or unmatch item based on result
  if (is_matched) {
    match_item <- list(list(name = as.character(candidate_toefl), matched = TRUE))
    unmatch_item <- list()
  } else {
    match_item <- list()
    unmatch_item <- list(list(name = as.character(candidate_toefl)))
  }

  # Calculate score
  level_score <- level_score_map[as.character(max_level)]
  if (is.na(level_score)) level_score <- 0
  final_score <- level_score * weight_toefl
  if (is.na(final_score)) final_score <- 0

  return(list(
    toefl_weight = final_score,
    match_item = match_item,
    unmatch_item = unmatch_item
  ))
}

# score_toefl <- function(df, config, base_line_toefl, toefl_column = "toefl_score", weight_toefl = NULL) {
#   # Level to score mapping
#   level_score_map <- c(
#     "5" = 1.0,    # 100%
#     "4" = 0.8,    # 80%
#     "3" = 0.6,    # 60%
#     "2" = 0.4,    # 40%
#     "1" = 0.2,    # 20%
#     "0" = 0.0     # 0%
#   )
#
#   # Check if TOEFL configuration exists in the config
#   if (!"TOEFL" %in% names(config)) {
#     message("TOEFL is not configured. Skipping TOEFL scoring.")
#     return(list(
#       toefl_weight = 0,
#       match_item = character(0),
#       unmatch_item = character(0)
#     ))
#   }
#
#   # Extract TOEFL configuration
#   toefl_config <- config$TOEFL
#   toefl_criteria <- toefl_config$MrC_TOEFL
#
#   # Get candidate TOEFL score
#   candidate_toefl <- df[[toefl_column]]
#
#   # Initialize results
#   match_items <- character(0)
#   unmatch_items <- character(0)
#   max_level <- 0
#
#   # Skip processing if TOEFL score or baseline is NA
#   if (is.na(candidate_toefl) || is.na(base_line_toefl)) {
#     return(list(
#       toefl_weight = 0,
#       match_item = match_items,
#       unmatch_item = unmatch_items
#     ))
#   }
#
#   # Check each TOEFL criterion
#   for (criterion in toefl_criteria) {
#     level <- criterion$level
#     value <- criterion$value
#
#     # Parse the score range from the level string
#     if (grepl("-", level)) {
#       # Case: "621-677"
#       range_parts <- as.numeric(strsplit(level, "-")[[1]])
#       min_score <- range_parts[1]
#       max_score <- range_parts[2]
#
#       if (candidate_toefl >= min_score && candidate_toefl <= max_score) {
#         match_items <- c(match_items, level)
#         if (value > max_level) max_level <- value
#       } else {
#         unmatch_items <- c(unmatch_items, level)
#       }
#     } else if (grepl("≤", level)) {
#       # Case: "≤ 380"
#       max_score <- as.numeric(gsub("≤ ", "", level))
#
#       if (candidate_toefl <= max_score) {
#         match_items <- c(match_items, level)
#         if (value > max_level) max_level <- value
#       } else {
#         unmatch_items <- c(unmatch_items, level)
#       }
#     }
#   }
#
#   # Convert level to score and apply weight
#   level_score <- level_score_map[as.character(max_level)]
#   if (is.na(level_score)) level_score <- 0
#
#   # Calculate final weighted score
#   final_score <- level_score * weight_toefl
#   if (is.na(final_score)) final_score <- 0
#
#   # Return results as a list
#   return(list(
#     toefl_weight = final_score,
#     match_item = match_items,
#     unmatch_item = unmatch_items
#   ))
# }


# Helper function to create matching_criteria for all 7 criteria
# Update create_matching_criteria to include Major
create_matching_criteria <- function(skill_match, skill_unmatch, domicile_match, domicile_unmatch,
                                     education_match, education_unmatch, experience_match, experience_unmatch,
                                     gpa_match, gpa_unmatch, salary_match, salary_unmatch, industry_match, industry_unmatch,
                                     major_match, major_unmatch,
                                     #<---Pos Enhance--->#
                                     age_match, age_unmatch,
                                     #<---Pos Enhance--->#
                                     toefl_match, toefl_unmatch
                                     ) {

  # Replace NULL or empty inputs with empty lists to avoid errors
  skill_match <- if (is.null(skill_match)) list() else skill_match
  skill_unmatch <- if (is.null(skill_unmatch)) list() else skill_unmatch
  domicile_match <- if (is.null(domicile_match)) list() else domicile_match
  domicile_unmatch <- if (is.null(domicile_unmatch)) list() else domicile_unmatch
  education_match <- if (is.null(education_match)) list() else education_match
  education_unmatch <- if (is.null(education_unmatch)) list() else education_unmatch
  experience_match <- if (is.null(experience_match)) list() else experience_match
  experience_unmatch <- if (is.null(experience_unmatch)) list() else experience_unmatch
  gpa_match <- if (is.null(gpa_match)) list() else gpa_match
  gpa_unmatch <- if (is.null(gpa_unmatch)) list() else gpa_unmatch
  salary_match <- if (is.null(salary_match)) list() else salary_match
  salary_unmatch <- if (is.null(salary_unmatch)) list() else salary_unmatch
  industry_match <- if (is.null(industry_match)) list() else industry_match
  industry_unmatch <- if (is.null(industry_unmatch)) list() else industry_unmatch
  major_match <- if (is.null(major_match)) list() else major_match
  major_unmatch <- if (is.null(major_unmatch)) list() else major_unmatch
  age_match <- if (is.null(age_match)) list() else age_match #<---Pos Enhance--->#
  age_unmatch <- if (is.null(age_unmatch)) list() else age_unmatch #<---Pos Enhance--->#
  toefl_match <- if (is.null(toefl_match)) list() else toefl_match #<---Pos Enhance--->#
  toefl_unmatch <- if (is.null(toefl_unmatch)) list() else toefl_unmatch #<---Pos Enhance--->#

  # Create the nested list structure for matching_criteria
  matching_criteria <- list(
    skill_and_tools = list(
      match_item = lapply(skill_match, function(item) list(name = item, matched = TRUE)),
      unmatch_item = lapply(skill_unmatch, function(item) list(name = item))
    ),
    domicile = list(
      match_item = lapply(domicile_match, function(item) list(name = item, matched = TRUE)),
      unmatch_item = lapply(domicile_unmatch, function(item) list(name = item))
    ),
    education = list(
      match_item = lapply(education_match, function(item) list(name = item, matched = TRUE)),
      unmatch_item = lapply(education_unmatch, function(item) list(name = item))
    ),
    working_experience = list(
      match_item = lapply(experience_match, function(item) list(name = item, matched = TRUE)),
      unmatch_item = lapply(experience_unmatch, function(item) list(name = item))
    ),
    GPA = list(
      match_item = lapply(gpa_match, function(item) list(name = item, matched = TRUE)),
      unmatch_item = lapply(gpa_unmatch, function(item) list(name = item))
    ),
    expected_salary = list(
      match_item = lapply(salary_match, function(item) list(name = item, matched = TRUE)),
      unmatch_item = lapply(salary_unmatch, function(item) list(name = item))
    ),
    industry = list(
      match_item = lapply(industry_match, function(item) list(name = item, matched = TRUE)),
      unmatch_item = lapply(industry_unmatch, function(item) list(name = item))
    ),
    major = list(
      match_item = lapply(major_match, function(item) list(name = item, matched = TRUE)),
      unmatch_item = lapply(major_unmatch, function(item) list(name = item))
    ),
    #<---Pos Enhance--->#
    age = list(  # Add age matching criteria
      match_item = lapply(age_match, function(item) list(name = item, matched = TRUE)),
      unmatch_item = lapply(age_unmatch, function(item) list(name = item))
    ),
    #<---Pos Enhance--->#
    toefl = list(  # Add age matching criteria
      match_item = lapply(toefl_match, function(item) list(name = item, matched = TRUE)),
      unmatch_item = lapply(toefl_unmatch, function(item) list(name = item))
    )
  )

  # Convert the list to JSON format
  return(toJSON(matching_criteria, auto_unbox = TRUE))
}


print("Done New Function")

print("Start New Calculation")

candidates <- if (nrow(candidates) > 0) {
  candidates %>%
    mutate(
      #<---Pos Enhance--->#
      MrU_Score_Age = as.numeric(unlist(mapply(function(age) {
        result <- score_age(
          data.frame(age = age),
          json_data,
          base_line_age,
          weight_age = scaled_values_list$weight_age
        )
        return(result$age_weight)
      }, age))),
      #<---Pos Enhance--->#
      MrU_Score_Toefl = as.numeric(unlist(mapply(function(toefl_score) {
        result <- score_toefl(
          data.frame(toefl_score = toefl_score),
          json_data,
          base_line_toefl,
          weight_toefl = scaled_values_list$weight_toefl
        )
        return(result$toefl_weight)
      }, toefl_score))),
      MrU_Score_Edu = as.numeric(unlist(mapply(function(education) {
        result <- score_education(education, base_line_edu, scaled_values_list$weight_education)
        if (is.null(result$score) || is.na(result$score)) return(0)  # Handle NULL or NA
        return(result$score)
      }, Education))),
      MrU_Score_WE = as.numeric(unlist(mapply(function(experience) {
        result <- score_working_experience(experience, base_line_we, scaled_values_list$weight_we)
        if (is.null(result$score) || is.na(result$score)) return(0)  # Handle NULL or NA
        return(result$score)
      }, Experience))),
      MrU_Score_GPA = as.numeric(unlist(mapply(function(gpa) {
        result <- score_gpa(gpa, base_line_gpa, scaled_values_list$weight_gpa)
        if (is.null(result$score) || is.na(result$score)) return(0)  # Handle NULL or NA
        return(result$score)
      }, GPA))),
      MrU_Score_Domisili = as.numeric(unlist(mapply(function(domisili) {
        result <- score_domisili(domisili, base_line_domisili, scaled_values_list$weight_domisili)
        if (is.null(result$score) || is.na(result$score)) return(0)  # Handle NULL or NA
        return(result$score)
      }, Domisili))),
      MrU_Score_ES = as.numeric(unlist(mapply(function(expected_salary) {
        result <- score_expected_salary(expected_salary, df_base_line$minimum_salary, df_base_line$maximum_salary, scaled_values_list$weight_es)
        if (is.null(result$score) || is.na(result$score)) return(0)  # Handle NULL or NA
        return(result$score)
      }, Expected_Salary))),
      MrU_Score_Industry = as.numeric(unlist(mapply(function(industry) {
        result <- score_industry(industry, base_line_industry, scaled_values_list$weight_industry)
        if (is.null(result$score) || is.na(result$score)) return(0)  # Handle NULL or NA
        return(result$score)
      }, Industry))),
      MrU_Score_Skillntools = as.numeric(unlist(mapply(function(skillntools) {
        result <- score_skillntools(skillntools, base_line_skillntools, scaled_values_list$weight_skill)
        if (is.null(result$score) || is.na(result$score)) return(0)  # Handle NULL or NA
        return(result$score)
      }, Skillntools))),
      Match_Item_Edu = mapply(function(education) {
        result <- score_education(education, base_line_edu, scaled_values_list$weight_education)
        return(result$education$match_item)
      }, Education, SIMPLIFY = FALSE),
      Unmatch_Item_Edu = mapply(function(education) {
        result <- score_education(education, base_line_edu, scaled_values_list$weight_education)
        return(result$education$unmatch_item)
      }, Education, SIMPLIFY = FALSE),
      Match_Item_WE = mapply(function(experience) {
        result <- score_working_experience(experience, base_line_we, scaled_values_list$weight_we)
        return(result$working_experience$match_item)
      }, Experience, SIMPLIFY = FALSE),
      Unmatch_Item_WE = mapply(function(experience) {
        result <- score_working_experience(experience, base_line_we, scaled_values_list$weight_we)
        return(result$working_experience$unmatch_item)
      }, Experience, SIMPLIFY = FALSE),
      Match_Item_GPA = mapply(function(gpa) {
        result <- score_gpa(gpa, base_line_gpa, scaled_values_list$weight_gpa)
        return(result$gpa$match_item)
      }, GPA, SIMPLIFY = FALSE),
      Unmatch_Item_GPA = mapply(function(gpa) {
        result <- score_gpa(gpa, base_line_gpa, scaled_values_list$weight_gpa)
        return(result$gpa$unmatch_item)
      }, GPA, SIMPLIFY = FALSE),
      Match_Item_Domisili = mapply(function(domisili) {
        result <- score_domisili(domisili, base_line_domisili, scaled_values_list$weight_domisili)
        return(result$domicile$match_item)
      }, Domisili, SIMPLIFY = FALSE),
      Unmatch_Item_Domisili = mapply(function(domisili) {
        result <- score_domisili(domisili, base_line_domisili, scaled_values_list$weight_domisili)
        return(result$domicile$unmatch_item)
      }, Domisili, SIMPLIFY = FALSE),
      Match_Item_ES = mapply(function(expected_salary) {
        result <- score_expected_salary(expected_salary, df_base_line$minimum_salary, df_base_line$maximum_salary, scaled_values_list$weight_es)
        return(result$expected_salary$match_item)
      }, Expected_Salary, SIMPLIFY = FALSE),
      Unmatch_Item_ES = mapply(function(expected_salary) {
        result <- score_expected_salary(expected_salary, df_base_line$minimum_salary, df_base_line$maximum_salary, scaled_values_list$weight_es)
        return(result$expected_salary$unmatch_item)
      }, Expected_Salary, SIMPLIFY = FALSE),
      Match_Item_Industry = mapply(function(industry) {
        result <- score_industry(industry, base_line_industry, scaled_values_list$weight_industry)
        return(result$industry$match_item)
      }, Industry, SIMPLIFY = FALSE),
      Unmatch_Item_Industry = mapply(function(industry) {
        result <- score_industry(industry, base_line_industry, scaled_values_list$weight_industry)
        return(result$industry$unmatch_item)
      }, Industry, SIMPLIFY = FALSE),
      Match_Item_Skillntools = mapply(function(skillntools) {
        result <- score_skillntools(skillntools, base_line_skillntools, scaled_values_list$weight_skill)
        return(result$skill_and_tools$match_item)
      }, Skillntools, SIMPLIFY = FALSE),
      Unmatch_Item_Skillntools = mapply(function(skillntools) {
        result <- score_skillntools(skillntools, base_line_skillntools, scaled_values_list$weight_skill)
        return(result$skill_and_tools$unmatch_item)
      }, Skillntools, SIMPLIFY = FALSE),
      # Add Major Detection for Matching Criteria
      Match_Item_Major = mapply(function(candidate_major) {
        result <- detect_major_match_items(candidate_major, base_line_major)
        return(result$match_item)
      }, Major, SIMPLIFY = FALSE),
      Unmatch_Item_Major = mapply(function(candidate_major) {
        result <- detect_major_match_items(candidate_major, base_line_major)
        return(result$unmatch_item)
      }, Major, SIMPLIFY = FALSE),


      #<---Pos Enhance--->#
      Match_Item_Age = mapply(function(age) {
        result <- score_age(
          data.frame(age = age),
          json_data,
          base_line_age,
          weight_age = scaled_values_list$weight_age
        )
        return(result$match_item)
      }, age, SIMPLIFY = FALSE),

      Unmatch_Item_Age = mapply(function(age) {
        result <- score_age(
          data.frame(age = age),
          json_data,
          base_line_age,
          weight_age = scaled_values_list$weight_age
        )
        return(result$unmatch_item)
      }, age, SIMPLIFY = FALSE),

      Match_Item_Toefl = mapply(function(toefl_score) {
        result <- score_toefl(
          data.frame(toefl_score = toefl_score),
          json_data,
          base_line_toefl,
          weight_toefl = scaled_values_list$weight_toefl
        )
        return(result$match_item)
      }, toefl_score, SIMPLIFY = FALSE),

      Unmatch_Item_Toefl = mapply(function(toefl_score) {
        result <- score_toefl(
          data.frame(toefl_score = toefl_score),
          json_data,
          base_line_toefl,
          weight_toefl = scaled_values_list$weight_toefl
        )
        return(result$unmatch_item)
      }, toefl_score, SIMPLIFY = FALSE),

      # #<---Pos Enhance--->#
      # Match_Item_Age = mapply(function(age) {
      #   result <- score_age(
      #     data.frame(age = age),
      #     json_data,
      #     base_line_age,
      #     weight_age = scaled_values_list$weight_age
      #   )
      #   return(list(result$match_item))
      # }, Age, SIMPLIFY = FALSE),
      #
      # Unmatch_Item_Age = mapply(function(age) {
      #   result <- score_age(
      #     data.frame(age = age),
      #     json_data,
      #     base_line_age,
      #     weight_age = scaled_values_list$weight_age
      #   )
      #   return(list(result$unmatch_item))
      # }, Age, SIMPLIFY = FALSE),
      #
      # #<---Pos Enhance--->#
      # Match_Item_Toefl = mapply(function(toefl_score) {
      #   result <- score_toefl(
      #     data.frame(toefl_score = toefl_score),
      #     json_data,
      #     base_line_toefl,
      #     weight_toefl = scaled_values_list$weight_toefl
      #   )
      #   return(list(result$match_item))
      # }, toefl_score, SIMPLIFY = FALSE),
      #
      # Unmatch_Item_Toefl = mapply(function(toefl_score) {
      #   result <- score_toefl(
      #     data.frame(toefl_score = toefl_score),
      #     json_data,
      #     base_line_toefl,
      #     weight_toefl = scaled_values_list$weight_toefl
      #   )
      #   return(list(result$unmatch_item))
      # }, toefl_score, SIMPLIFY = FALSE),

      # Check if 'Education_Major' exists and apply weight
      #<---TMMIN_Updated--->#
      MrU_Score_Edu = ifelse(!is.na(edu_major_weight), MrU_Score_Edu * edu_major_weight, MrU_Score_Edu),

      #<---Pos Enhance--->#
      MrU_Score_Age = MrU_Score_Age,  # ifelse(!is.na(age_weight), MrU_Score_Age * age_weight, MrU_Score_Age),

      #<---Pos Enhance--->#
      MrU_Score_Toefl = MrU_Score_Toefl,  # ifelse(!is.na(age_weight), MrU_Score_Age * age_weight, MrU_Score_Age),

      Total_Score = (MrU_Score_Toefl + MrU_Score_Age + MrU_Score_Edu + MrU_Score_WE + MrU_Score_GPA + MrU_Score_Domisili + MrU_Score_ES + MrU_Score_Industry + MrU_Score_Skillntools),
      MrU_Score_Edu = floor(MrU_Score_Edu),
      MrU_Score_WE = floor(MrU_Score_WE),
      MrU_Score_GPA = floor(MrU_Score_GPA),
      MrU_Score_Domisili = floor(MrU_Score_Domisili),
      MrU_Score_ES = floor(MrU_Score_ES),
      MrU_Score_Industry = floor(MrU_Score_Industry),
      MrU_Score_Skillntools = floor(MrU_Score_Skillntools),
      MrU_Score_Age = floor(MrU_Score_Age), #<---Pos Enhance--->#
      MrU_Score_Toefl = floor(MrU_Score_Toefl), #<---Pos Enhance--->#
      Total_Score = floor(Total_Score)
    )
} else {
  candidates  # Return the empty dataframe as is
}

print("Test New Create List Matching Criteria")

create_matching_criteria <- function(skill_match, skill_unmatch, domicile_match, domicile_unmatch,
                                     education_match, education_unmatch, experience_match, experience_unmatch,
                                     gpa_match, gpa_unmatch, salary_match, salary_unmatch, industry_match, industry_unmatch,
                                     major_match, major_unmatch,
                                     #<---Pos Enhance--->#
                                     age_match, age_unmatch,
                                     #<---Pos Enhance--->#
                                     toefl_match, toefl_unmatch
                                     ) {
  # Helper function to wrap items in a list (even if it's a single item)
  format_item <- function(item, include_matched = TRUE) {
    if (is.null(item) || length(item) == 0) {
      return(list())  # Return an empty list for NULL or empty items
    } else if (is.list(item) && length(item) > 0 && is.list(item[[1]])) {
      # For nested lists (like skill_and_tools) - handle multiple items
      return(item)
    } else if (is.list(item) && !is.null(item$name)) {
      # For single-item lists with a 'name' field (like gpa and working_experience)
      # Ensure it's wrapped in a list even for single items
      return(list(item))
    } else {
      # For simple values (numbers, strings), also ensure wrapping in a list
      return(list(list(name = item, matched = include_matched)))
    }
  }

  # Build the matching criteria using the helper function
  matching_criteria <- list(
    expected_salary = list(
      match_item = format_item(salary_match),
      unmatch_item = format_item(salary_unmatch, FALSE)
    ),
    industry = list(
      match_item = format_item(industry_match),
      unmatch_item = format_item(industry_unmatch, FALSE)
    ),
    domicile = list(
      match_item = if(length(domicile_match) > 0) domicile_match else list(),
      unmatch_item = if(length(domicile_unmatch) > 0) domicile_unmatch else list()
    ),
    gpa = list(
      match_item = format_item(gpa_match),
      unmatch_item = format_item(gpa_unmatch, FALSE)
    ),
    skill_and_tools = list(
      match_item = format_item(skill_match),
      unmatch_item = format_item(skill_unmatch, FALSE)
    ),
    working_experience = list(
      match_item = format_item(experience_match),
      unmatch_item = format_item(experience_unmatch, FALSE)
    ),
    education = list(
      match_item = format_item(education_match),
      unmatch_item = format_item(education_unmatch, FALSE)
    ),
    major = list(  # Add major matching criteria
      match_item = format_item(major_match),
      unmatch_item = format_item(major_unmatch, FALSE)
    ),
    #<---Pos Enhance--->#
    age = list(  # Add age matching criteria
      match_item = format_item(age_match),
      unmatch_item = format_item(age_unmatch, FALSE)
    ),
    #<---Pos Enhance--->#
    toefl = list(  # Add age matching criteria
      match_item = format_item(toefl_match),
      unmatch_item = format_item(toefl_unmatch, FALSE)
    )
  )
  return(matching_criteria)
}




# Function to check if Major exists in config and process accordingly
process_major_field <- function(match_item_major, unmatch_item_major, config) {
  if ("Major" %in% names(config)) {
    return(list(
      match_item_major = match_item_major,
      unmatch_item_major = unmatch_item_major
    ))
  } else {
    # Skip processing if Major doesn't exist in config
    return(list(
      match_item_major = NULL,
      unmatch_item_major = NULL
    ))
  }
}

#<---Pos Enhance--->#
# Function to check if Age exists in config and process accordingly
process_age_field <- function(match_item_age, unmatch_item_age, config) {
  if ("Age" %in% names(config)) {
    return(list(
      match_item_age = match_item_age,
      unmatch_item_age = unmatch_item_age
    ))
  } else {
    # Skip processing if Age doesn't exist in config
    return(list(
      match_item_age = NULL,
      unmatch_item_age = NULL
    ))
  }
}

#<---Pos Enhance--->#
# Function to check if Age exists in config and process accordingly
process_toefl_field <- function(match_item_toefl, unmatch_item_toefl, config) {
  if ("TOEFL" %in% names(config)) {
    return(list(
      match_item_toefl = match_item_toefl,
      unmatch_item_toefl = unmatch_item_toefl
    ))
  } else {
    # Skip processing if Age doesn't exist in config
    return(list(
      match_item_toefl = NULL,
      unmatch_item_toefl = NULL
    ))
  }
}

# Use mapply to apply `create_matching_criteria` and process Major conditionally
candidates$matching_criteria <- mapply(
  function(match_item_skillntools, unmatch_item_skillntools,
           match_item_domisili, unmatch_item_domisili,
           match_item_edu, unmatch_item_edu,
           match_item_we, unmatch_item_we,
           match_item_gpa, unmatch_item_gpa,
           match_item_es, unmatch_item_es,
           match_item_industry, unmatch_item_industry,
           match_item_major, unmatch_item_major,
           #<---Pos Enhance--->#
           match_item_age, unmatch_item_age,
           #<---Pos Enhance--->#
           match_item_toefl, unmatch_item_toefl
           ) {

    # Process Major field conditionally
    major_processed <- process_major_field(match_item_major, unmatch_item_major, json_data)
    #<---Pos Enhance--->#
    age_processed <- process_age_field(match_item_age, unmatch_item_age, json_data)
    #<---Pos Enhance--->#
    toefl_processed <- process_toefl_field(match_item_toefl, unmatch_item_toefl, json_data)

    # Call create_matching_criteria with all items, including processed Major
    create_matching_criteria(
      match_item_skillntools, unmatch_item_skillntools,
      match_item_domisili, unmatch_item_domisili,
      match_item_edu, unmatch_item_edu,
      match_item_we, unmatch_item_we,
      match_item_gpa, unmatch_item_gpa,
      match_item_es, unmatch_item_es,
      match_item_industry, unmatch_item_industry,
      major_processed$match_item_major, major_processed$unmatch_item_major,
      age_processed$match_item_age, age_processed$unmatch_item_age, #<---Pos Enhance--->#
      toefl_processed$match_item_toefl, toefl_processed$unmatch_item_toefl #<---Pos Enhance--->#
    )
  },
  candidates$Match_Item_Skillntools, candidates$Unmatch_Item_Skillntools,
  candidates$Match_Item_Domisili, candidates$Unmatch_Item_Domisili,
  candidates$Match_Item_Edu, candidates$Unmatch_Item_Edu,
  candidates$Match_Item_WE, candidates$Unmatch_Item_WE,
  candidates$Match_Item_GPA, candidates$Unmatch_Item_GPA,
  candidates$Match_Item_ES, candidates$Unmatch_Item_ES,
  candidates$Match_Item_Industry, candidates$Unmatch_Item_Industry,
  candidates$Match_Item_Major, candidates$Unmatch_Item_Major, # Add Major fields here
  candidates$Match_Item_Age, candidates$Unmatch_Item_Age, #<---Pos Enhance--->#
  candidates$Match_Item_Toefl, candidates$Unmatch_Item_Toefl, #<---Pos Enhance--->#
  SIMPLIFY = FALSE
)


#View(candidates$matching_criteria)

candidates_mru <- candidates

#View(candidates_mru)

print("Done New Calculation")

print("Test New Code Done")

print(candidates)
#View(candidates)

print("Cal MrU Done")

## 2.2. MrC

### 2.2.1 Scale MrC
print("Scale MrC")
# Function to scale non-zero MrC values
scale_mrc_values <- function(mrc_data) {
  # Filter out non-zero values and get their names
  non_zero_values_mrc <- mrc_data$value[mrc_data$value != 0]

  # Sum the non-zero values
  sum_non_zero <- sum(non_zero_values_mrc)

  # Determine the scaling factor to make the sum equal to 100
  scaling_factor <- 100 / sum_non_zero

  # Scale the non-zero values to make their sum 100
  scaled_values <- non_zero_values_mrc * scaling_factor

  # Update the MrC data with scaled values
  mrc_data$value[mrc_data$value != 0] <- scaled_values

  return(mrc_data)
}


# Apply scaling to each MrC criterion
scaled_MrC_Education <- scale_mrc_values(matchmaking_config$Education$MrC_Education)
#scaled_MrC_WE <- scale_mrc_values(matchmaking_config$Working_Experience$MrC_WE)
scaled_MrC_WE <- scale_mrc_values(matchmaking_config$Working_Experience$MrC_WE[-nrow(matchmaking_config$Working_Experience$MrC_WE), ])
scaled_MrC_GPA <- scale_mrc_values(matchmaking_config$GPA$MrC_GPA)

# Update the matchmaking_config with scaled MrC values
matchmaking_config$Education$MrC_Education <- scaled_MrC_Education
matchmaking_config$Working_Experience$MrC_WE <- scaled_MrC_WE
matchmaking_config$GPA$MrC_GPA <- scaled_MrC_GPA

max_MrC_Edu <- matchmaking_config$Education$MrC_Education$value |>
  tail(1)

max_MrC_WE <- matchmaking_config$Working_Experience$MrC_WE$value |>
  tail(1)

max_MrC_GPA <- matchmaking_config$GPA$MrC_GPA$value |>
  tail(1)

total_max_MrC <- max_MrC_Edu + max_MrC_WE + max_MrC_GPA

scale_value_MrC <- 100/total_max_MrC

matchmaking_config$Education$MrC_Education <- matchmaking_config$Education$MrC_Education |>
  mutate(scale_factor = scale_value_MrC,
         new_value = value * scale_value_MrC
  )

matchmaking_config$Working_Experience$MrC_WE <- matchmaking_config$Working_Experience$MrC_WE |>
  mutate(scale_factor = scale_value_MrC,
         new_value = value * scale_value_MrC
  )

matchmaking_config$GPA$MrC_GPA <- matchmaking_config$GPA$MrC_GPA |>
  mutate(scale_factor = scale_value_MrC,
         new_value = value * scale_value_MrC
  )

print(matchmaking_config$Working_Experience$MrC_WE)
#print(a)

print("Scale MrC Done")

print("Start MrC New Code")

# Helper function for MrC Education scoring

score_mrc_education <- function(candidate_edu, base_line, scaled_values) {
  # Check if candidate education or baseline is NA
  if (is.na(candidate_edu) || is.na(base_line)) {
    return(list(
      score = 0,
      match_item = list(),
      unmatch_item = list(),
      additional_value = NULL
    ))
  }

  edu_index <- match(candidate_edu, scaled_values$level)
  base_index <- match(base_line, scaled_values$level)

  # Check if either candidate or baseline index is NA
  if (is.na(edu_index) || is.na(base_index)) {
    return(list(
      score = 0,
      match_item = list(),
      unmatch_item = list(),
      additional_value = NULL
    ))
  }

  # If candidate education is higher than the baseline
  if (edu_index > base_index) {
    score <- scaled_values$new_value[edu_index]
    return(list(
      score = score,
      match_item = list(list(name = candidate_edu, matched = TRUE)),
      unmatch_item = list(),
      additional_value = candidate_edu
    ))
  }

  # If candidate education is less than or equal to the baseline
  return(list(
    score = 0,
    match_item = list(),
    unmatch_item = list(),
    additional_value = NULL
  ))
}

# Helper function for MrC Work Experience scoring


we_levels <- head(we_levels, -1)
print(we_levels)

# Enhance all_level_experience
score_mrc_work_experience <- function(candidate_we, base_line, scaled_values) {
  if (is.na(base_line)) {
    return(list(score = 0, match_item = list(), unmatch_item = list(), additional_value = NULL))
  }

  # Handle "all_level_experience": no MrC, only MrU
  if (base_line == "0-99 tahun") {
    return(list(score = 0, match_item = list(), unmatch_item = list(), additional_value = NULL))
  }

    # Convert the baseline string to a numeric range
    base_range <- convert_to_numeric_range(gsub(" tahun", "", base_line))

    # Initialize match_item and unmatch_item as empty lists
    match_items <- list()
    unmatch_items <- list()

  # # Convert candidate_we to numeric for comparison
  # candidate_we <- as.numeric(gsub(" tahun", "", candidate_we))

  # Determine the candidate's level
  candidate_level <- names(we_levels)[which(sapply(we_levels, function(x) {
    candidate_we >= x[1] && candidate_we <= x[2]
  }))]

  # Find the corresponding scaled value for MrC
  candidate_scaled_value <- scaled_values$new_value[scaled_values$level == candidate_level]
  if (length(candidate_scaled_value) == 0) {
    candidate_scaled_value <- 0  # Default to 0 if no matching level found
  }

  # Convert baseline to a numeric range
  base_range <- convert_to_numeric_range(gsub(" tahun", "", base_line))

  # If the candidate experience meets or exceeds baseline, return MrC score
    if (candidate_we > base_range[2]) {
      # Candidate exceeds the maximum baseline
      match_items <- list(list(name = paste(candidate_we, "tahun")))
      score <- candidate_scaled_value
      additional_value <- candidate_we
    } else if (candidate_we < base_range[1]) {
      # Candidate is below the minimum baseline
      unmatch_items <- list(list(name = paste(candidate_we, "tahun")))
      score <- 0
      additional_value <- NULL
    } else {
      # Candidate is within the baseline range
      score <- 0
      additional_value <- NULL
    }

    return(list(
      score = score,
      match_item = match_items,
      unmatch_item = unmatch_items,
      additional_value = additional_value
    ))
}

# Helper function for MrC GPA scoring
score_mrc_gpa <- function(candidate_gpa, base_line, scaled_values) {
  # Initialize empty lists for match and unmatch items
  match_items <- list()
  unmatch_items <- list()

  # Check if the baseline GPA is NA or if the candidate GPA is missing
  if (is.na(base_line) || is.na(candidate_gpa)) {
    return(list(score = 0, match_item = match_items, unmatch_item = unmatch_items, additional_value = NULL))
  }

  # Convert baseline to numeric range
  base_range <- convert_to_numeric_range(base_line)

  # Determine the candidate's level
  candidate_level <- names(gpa_levels)[which(sapply(gpa_levels, function(x) {
    candidate_gpa >= x[1] && candidate_gpa <= x[2]
  }))]

  # Handle cases where no matching GPA level is found
  if (length(candidate_level) != 1) {
    unmatch_items <- list(list(name = paste(candidate_gpa)))
    return(list(score = 0, match_item = match_items, unmatch_item = unmatch_items, additional_value = NULL))
  }

  # Find the corresponding scaled value for the candidate's GPA level
  candidate_scaled_value <- scaled_values$new_value[scaled_values$level == candidate_level]
  if (length(candidate_scaled_value) == 0) {
    candidate_scaled_value <- 0  # Default to 0 if no matching level found
  }

  # Determine if the candidate's GPA exceeds, is below, or within the baseline range
  if (candidate_gpa > base_range[2]) {
    # Candidate GPA exceeds the maximum baseline
    match_items <- list(list(name = paste(candidate_gpa)))
    score <- candidate_scaled_value
    additional_value <- candidate_gpa
  } else if (candidate_gpa < base_range[1]) {
    # Candidate GPA is below the minimum baseline
    unmatch_items <- list(list(name = paste(candidate_gpa)))
    score <- 0
    additional_value <- NULL
  } else {
    # Candidate GPA is within the baseline range
    match_items <- list(list(name = paste(candidate_gpa)))
    score <- 0
    additional_value <- NULL
  }

  # Return the results
  return(list(
    score = score,
    match_item = match_items,
    unmatch_item = unmatch_items,
    additional_value = additional_value
  ))
}


# score_mrc_gpa <- function(candidate_gpa, base_line, scaled_values) {
#   # Initialize empty lists for match and unmatch items
#   match_items <- list()
#   unmatch_items <- list()
#
#   # Check if the baseline GPA is NA or if the candidate GPA is missing
#   if (is.na(base_line) || is.na(candidate_gpa)) {
#     return(list(score = 0, match_item = match_items, unmatch_item = unmatch_items, additional_value = NULL))
#   }
#
#   # Convert baseline to numeric range
#   base_range <- convert_to_numeric_range(base_line)
#
#   # Determine the candidate's level
#   gpa_level <- names(gpa_levels)[which(sapply(gpa_levels, function(x) {
#     candidate_gpa >= x[1] && candidate_gpa <= x[2]
#   }))]
#
#   # Handle cases with no matching GPA level
#   if (length(gpa_level) != 1) {
#     return(list(score = 0, match_item = match_items, unmatch_item = unmatch_items, additional_value = NULL))
#   }
#
#   # Find the corresponding scaled value for the candidate's GPA level
#   gpa_info <- scaled_values %>% filter(level == gpa_level)
#
#   # Handle cases with no matching scaled value
#   if (nrow(gpa_info) != 1) {
#     return(list(score = 0, match_item = match_items, unmatch_item = unmatch_items, additional_value = NULL))
#   }
#
#   # Calculate the score using the scaled value
#   score <- gpa_info$new_value[1]
#
#   # Determine if the candidate's GPA is above, below, or within the baseline range
#   if (candidate_gpa > base_range[2]) {
#     match_items <- list(list(name = paste(candidate_gpa)))
#     additional_value <- candidate_gpa
#   } else if (candidate_gpa < base_range[1]) {
#     unmatch_items <- list(list(name = paste(candidate_gpa)))
#     score <- 0
#     additional_value <- NULL
#   } else {
#     additional_value <- NULL
#   }
#
#   return(list(
#     score = score,
#     match_item = match_items,
#     unmatch_item = unmatch_items,
#     additional_value = additional_value
#   ))
# }

# Main function to calculate MrC scores # BUG

calculate_mrc_scores <- function(candidate, matchmaking_config) {
  edu_result <- score_mrc_education(candidate$Education, base_line_edu, matchmaking_config$Education$MrC_Education)
  we_result <- score_mrc_work_experience(candidate$Experience, base_line_we, matchmaking_config$Working_Experience$MrC_WE)
  gpa_result <- score_mrc_gpa(candidate$GPA, base_line_gpa, matchmaking_config$GPA$MrC_GPA)
  total_score <- edu_result$score + we_result$score + gpa_result$score

  create_matching_criteria_list <- function(result, field_name) {
    list(
      unmatch_item = if (!is.null(result$unmatch_item)) lapply(result$unmatch_item, function(x) list(name = x$name)) else list(),
      match_item = if (!is.null(result$match_item)) lapply(result$match_item, function(x) list(name = x$name)) else list()
    )
  }

  matching_criteria <- list(
    education = create_matching_criteria_list(edu_result, "education"),
    experience = create_matching_criteria_list(we_result, "experience"),
    gpa = create_matching_criteria_list(gpa_result, "gpa"),
    skill = list(unmatch_item = list(), match_item = list()),
    domicile = list(unmatch_item = list(), match_item = list()),
    salary = list(unmatch_item = list(), match_item = list()),
    industry = list(unmatch_item = list(), match_item = list())
  )

  return(list(
    Total_MrC_Score = round(total_score, 0),
    MrC_Score_Edu = round(edu_result$score, 0),
    MrC_Score_WE = round(we_result$score, 0),
    MrC_Score_GPA = round(gpa_result$score, 0),
    matching_criteria = matching_criteria
  ))
}


candidates <- if (nrow(candidates) > 0) {
  candidates %>%
    rowwise() %>%
    mutate(
      MrC_results = list(calculate_mrc_scores(cur_data(), matchmaking_config))
    ) %>%
    ungroup() %>%
    mutate(
      Total_MrC_Score = map_dbl(MrC_results, "Total_MrC_Score"),
      MrC_Score_Edu = map_dbl(MrC_results, "MrC_Score_Edu"),
      MrC_Score_WE = map_dbl(MrC_results, "MrC_Score_WE"),
      MrC_Score_GPA = map_dbl(MrC_results, "MrC_Score_GPA"),
      additional_values = map(MrC_results, "matching_criteria") |>
        map(~{
          if (!is.null(.x$MrC_Score_Edu) && !is.na(.x$MrC_Score_Edu) &&
              length(.x$education$match_item) > 0 && .x$MrC_Score_Edu == 0) {
            .x$education$match_item <- list()
          }
          if (!is.null(.x$MrC_Score_WE) && !is.na(.x$MrC_Score_WE) &&
              length(.x$experience$match_item) > 0 && .x$MrC_Score_WE == 0) {
            .x$experience$match_item <- list()
          }
          return(.x)
        })
    )
} else {
  candidates  # Return the empty dataframe as is
}

candidate_mrc <- candidates
#View(candidate_mrc)

#print(candidates_with_mrc)

#View(candidates_with_mrc)
#View(candidates)

print("Done MrC New Code")

print("Cal MrC Done")

# 3. Finalized Table
print("Finalized Table")

candidates <- if (nrow(candidates) > 0) {
  candidates %>%
  rename(Total_MrU_Score = Total_Score) |>
  mutate(
    Final_Score = paste(Total_MrU_Score, Total_MrC_Score, sep = "/")) |>
  arrange(desc(Total_MrU_Score), desc(Total_MrC_Score))
} else {
  candidates  # Return the empty dataframe as is
}

#View(candidates)



# Print results
# print(candidates)

df_result <- candidates

df_result <- if (nrow(df_result) > 0) {
  df_result %>%
  arrange(desc(Total_MrU_Score), desc(Total_MrC_Score)) |>
  rename(main_score = Total_MrU_Score,
         additional_score = Total_MrC_Score) |>
  select(user_job_vacancy_id, main_score, additional_score, matching_criteria, additional_values)
} else {
  df_result  # Return the empty dataframe as is
}

#print(candidates_stop)

#View(df_result)
#View(df_result$matching_criteria$Value)
glimpse(df_result)
print(df_result)
print("Finalized Done")

# DB Write
print("Write for DB")


result_list <- list()

# Check if the dataframe is empty before starting the loop
if (nrow(df_result) > 0) {
  for (i in 1:nrow(df_result)) {
    # Set schema for the query
    schema <- params$schema
    schema_sql <- paste0("SET search_path = ?schema;")
    safe_schema_sql <- sqlInterpolate(con_write, schema_sql, schema = schema)
    dbExecute(con_write, safe_schema_sql)

    # Convert matching_criteria and additional_values to JSON without extra escaping
    json_data_mru <- jsonlite::toJSON(df_result$matching_criteria[[i]], auto_unbox = TRUE, pretty = FALSE, null = "null")
    json_data_mrc <- jsonlite::toJSON(df_result$additional_values[[i]], auto_unbox = TRUE, pretty = FALSE, null = "null")

    # Handle single quotes for SQL
    escaped_json_mru <- gsub("'", "''", json_data_mru)
    escaped_json_mrc <- gsub("'", "''", json_data_mrc)

    cleaned_main_score <- ifelse(is.na(df_result$main_score[i]), 0, df_result$main_score[i])
    cleaned_additional_score <- ifelse(is.na(df_result$additional_score[i]), 0, df_result$additional_score[i])

    # Prepare the INSERT query with jsonb casting
    query <- paste0(
      'INSERT INTO user_vacancy_matchmaking_results (user_job_vacancy_id, main_score, additional_score, matching_criteria, additional_values, created_at, updated_at) VALUES (',
      df_result$user_job_vacancy_id[i], ", ",
      cleaned_main_score, ", ",
      cleaned_additional_score, ", ",
      "'", escaped_json_mru, "'::jsonb, ",  # No additional backslashes in JSON
      "'", escaped_json_mrc, "'::jsonb, ",  # No additional backslashes in JSON
      "NOW(), NOW()) ",
      "ON CONFLICT (user_job_vacancy_id) DO UPDATE SET ",
      "main_score = EXCLUDED.main_score, additional_score = EXCLUDED.additional_score, ",
      "matching_criteria = EXCLUDED.matching_criteria, additional_values = EXCLUDED.additional_values, updated_at = EXCLUDED.updated_at;"
    )

    # Execute the query inside the loop
    DBI::dbExecute(con_write, query)

    # Store the processed data in result_list for further operations if needed
    result_list[[i]] <- list(
      user_job_vacancy_id = df_result$user_job_vacancy_id[i],
      main_score = cleaned_main_score,
      additional_score = cleaned_additional_score,
      matching_criteria = escaped_json_mru, #json_data without gsub modifications
      additional_values = escaped_json_mrc #json_data without gsub modifications
    )
  }
} else {
  cat("No data available. Skipping database write process.\n")
}

# Convert the result list to a data frame
# df_result <- do.call(rbind, lapply(result_list, as.data.frame))

df_result <- do.call(rbind, lapply(result_list, function(x) {
  as.data.frame(
    x,
    stringsAsFactors = FALSE,
    row.names = "user_job_vacancy_id"
  )
}))
# Return the cleaned data frame
df_result


# Close connection
DBI::dbDisconnect(con_write)
DBI::dbDisconnect(con_read)


























































