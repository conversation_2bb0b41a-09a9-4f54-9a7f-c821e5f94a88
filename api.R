# Load required libraries
library(plumber)
library(dotenv)
library(dplyr)
library(readr)
library(tidyr)
library(lubridate)
library(zoo)
library(logger)

# Initialize logging
log_threshold(INFO)
log_appender(appender_file("logs/api.log"))

# Load environment variables
load_dot_env(".env")

# Source modular components
source("R/config/app_config.R")
source("R/api/middleware.R")
source("R/api/controllers/candidate_controller.R")
source("R/utils/response_helpers.R")
source("R/validators/input_validators.R")

#* @apiTitle Candidate Scoring and Matching API
#* @apiDescription Advanced API for candidate evaluation and job matching
#* @apiVersion 2.0.0
#* @apiContact list(name = "API Support", email = "<EMAIL>")

#* @filter cors
cors_filter <- function(req, res) {
  tryCatch({
    res$setHeader("Access-Control-Allow-Origin", "*")
    res$setHeader("Access-Control-Allow-Credentials", "true")

    if (req$REQUEST_METHOD == "OPTIONS") {
      res$setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
      res$setHeader("Access-Control-Allow-Headers", "Authorization, Content-Type, X-Requested-With")
      res$setHeader("Access-Control-Max-Age", "86400")
      res$status <- 200
      return(list())
    }

    plumber::forward()
  }, error = function(e) {
    log_error("CORS filter error: {e$message}")
    plumber::forward()
  })
}

#* @filter authenticate
auth_filter <- function(req, res) {
  tryCatch({
    # Skip authentication for health check, docs, and OPTIONS
    skip_paths <- c("^/health$", "^/__docs__/", "^/openapi.json$")
    if (any(sapply(skip_paths, function(pattern) grepl(pattern, req$PATH_INFO))) ||
        req$REQUEST_METHOD == "OPTIONS") {
      return(plumber::forward())
    }

    # Validate authorization header
    auth_result <- validate_auth_header(req$HTTP_AUTHORIZATION)
    if (!auth_result$valid) {
      log_warn("Authentication failed: {auth_result$message}")
      res$status <- 401
      return(create_error_response("Unauthorized", auth_result$message))
    }

    # Add user context to request
    req$user_context <- auth_result$user_context
    plumber::forward()

  }, error = function(e) {
    log_error("Authentication filter error: {e$message}")
    res$status <- 500
    return(create_error_response("Internal Server Error", "Authentication system error"))
  })
}

#* Process candidate experience data
#* @param job_vacancy_id:int The ID of the job vacancy
#* @param schema:string Tenant database schema
#* @param recalculate_all:logical Whether to recalculate all candidates or only new ones
#* @post /data_experience_processing/<job_vacancy_id:int>/<schema:string>/<recalculate_all:logical>
#* @security bearerAuth
process_candidate_experience


#* Advanced candidate matching process
#* @param job_vacancy_id:int The ID of the job vacancy
#* @param schema:string Tenant database schema
#* @param recalculate_all:logical Whether to recalculate all matches or only new ones
#* @post /match_making/<job_vacancy_id:int>/<schema:string>/<recalculate_all:logical>
#* @security bearerAuth
process_candidate_matching

#* Get job vacancy configuration
#* @param job_vacancy_id:int The ID of the job vacancy
#* @param schema:string Tenant database schema
#* @get /job_vacancy/<job_vacancy_id:int>/<schema:string>/config
#* @security bearerAuth
get_job_vacancy_config

#* Get processing status
#* @param job_id:string The processing job identifier
#* @get /processing_status/<job_id:string>
#* @security bearerAuth
get_processing_status

#* Get API statistics
#* @get /stats
#* @security bearerAuth
get_api_statistics

#* Health check endpoint
#* @get /health
health_check_endpoint



#* @plumber
function(pr) {
  # Set up middleware
  pr %>%
    pr_filter("cors", cors_filter) %>%
    pr_filter("auth", auth_filter) %>%
    pr_filter("logger", request_logger) %>%
    pr_filter("rate_limit", rate_limiter) %>%
    pr_filter("input_sanitizer", input_sanitizer) %>%
    pr_set_error(error_handler) %>%
    pr_set_api_spec(function(spec) {
      spec$info$title <- "Candidate Scoring and Matching API"
      spec$info$description <- "Advanced API for candidate evaluation and job matching with comprehensive scoring algorithms"
      spec$info$version <- "2.0.0"
      spec$info$contact <- list(
        name = "API Support",
        email = "<EMAIL>"
      )
      spec$components$securitySchemes <- list(
        bearerAuth = list(
          type = "http",
          scheme = "bearer",
          description = "Bearer token authentication"
        )
      )
      spec$security <- list(list(bearerAuth = list()))
      return(spec)
    })
}
