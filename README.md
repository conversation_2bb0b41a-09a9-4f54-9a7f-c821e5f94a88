# Candidate Scoring and Matching API v2.0.0

A comprehensive, refactored candidate evaluation and job matching system built with R and <PERSON>lumber, following modern software engineering best practices.

## 🚀 Features

- **Advanced Scoring Algorithms**: Multi-factor candidate evaluation (education, experience, GPA, age, TOEFL)
- **Intelligent Matching**: Sophisticated job-candidate compatibility scoring
- **Robust Architecture**: Layered design with clear separation of concerns
- **Comprehensive Security**: Input validation, authentication, rate limiting, SQL injection prevention
- **High Performance**: Connection pooling, batch processing, optimized queries
- **Extensive Testing**: Unit tests with high coverage
- **Production Ready**: Logging, monitoring, health checks, graceful shutdown

## 📋 Table of Contents

- [Architecture](#architecture)
- [Installation](#installation)
- [Configuration](#configuration)
- [API Documentation](#api-documentation)
- [Testing](#testing)
- [Deployment](#deployment)
- [Development](#development)
- [Contributing](#contributing)

## 🏗️ Architecture

### Layered Architecture
```
┌─────────────────────────────────────────┐
│              API Layer                  │
│  Controllers, Middleware, Validation    │
├─────────────────────────────────────────┤
│           Business Logic Layer          │
│     Services, Algorithms, Rules         │
├─────────────────────────────────────────┤
│           Data Access Layer             │
│    Repositories, Database Management    │
├─────────────────────────────────────────┤
│              Model Layer                │
│      Data Models, DTOs, Entities        │
└─────────────────────────────────────────┘
```

### Key Components

- **API Controllers**: Handle HTTP requests and responses
- **Services**: Contain business logic for scoring and matching
- **Repositories**: Manage data access and database operations
- **Models**: Define data structures and validation rules
- **Utilities**: Reusable algorithms and helper functions
- **Middleware**: Cross-cutting concerns (auth, logging, validation)

## 🛠️ Installation

### Prerequisites

- R (>= 4.0.0)
- PostgreSQL (>= 12.0)
- Docker (optional, for containerized deployment)

### Required R Packages

```r
install.packages(c(
  "plumber", "DBI", "RPostgres", "pool", "dplyr", 
  "jsonlite", "logger", "R6", "dotenv", "lubridate",
  "zoo", "testthat", "digest"
))
```

### Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd scoring-candidate
   ```

2. **Install dependencies**
   ```r
   # In R console
   renv::restore()  # If using renv
   # OR
   source("install_packages.R")
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Set up database**
   ```sql
   -- Create database and run migrations
   CREATE DATABASE candidate_scoring;
   -- Run your schema setup scripts
   ```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file with the following variables:

```bash
# Application Configuration
APP_NAME=Candidate Scoring API
APP_VERSION=2.0.0
APP_ENV=development
APP_PORT=5656
APP_HOST=0.0.0.0
LOG_LEVEL=INFO

# Database Configuration (Read)
DB_NAME_READ=candidate_scoring
DB_HOST_READ=localhost
DB_PORT_READ=5432
DB_USER_READ=readonly_user
DB_PASSWORD_READ=readonly_password
DB_POOL_SIZE_READ=5
DB_TIMEOUT_READ=30

# Database Configuration (Write)
DB_NAME_WRITE=candidate_scoring
DB_HOST_WRITE=localhost
DB_PORT_WRITE=5432
DB_USER_WRITE=readwrite_user
DB_PASSWORD_WRITE=readwrite_password
DB_POOL_SIZE_WRITE=3
DB_TIMEOUT_WRITE=30

# AWS Configuration
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_DEFAULT_REGION=us-east-1
AWS_S3_BUCKET=your-bucket-name

# Security Configuration
VALID_TOKEN=your_secure_token_here
TOKEN_EXPIRY_HOURS=24
RATE_LIMIT_PER_MINUTE=100

# Scoring Configuration
DEFAULT_CUTOFF_VALUE=40
MAX_BATCH_SIZE=1000
CACHE_TTL_MINUTES=60
```

## 📚 API Documentation

### Base URL
```
http://localhost:5656
```

### Authentication
All endpoints (except `/health`) require Bearer token authentication:
```
Authorization: Bearer <your_token>
```

### Endpoints

#### 1. Health Check
```http
GET /health
```
Returns system health status including database connectivity.

#### 2. Process Candidate Experience
```http
POST /data_experience_processing/{job_vacancy_id}/{schema}/{recalculate_all}
```
Processes candidate experience data and calculates scores.

**Parameters:**
- `job_vacancy_id` (int): Job vacancy identifier
- `schema` (string): Database schema name
- `recalculate_all` (boolean): Whether to recalculate all candidates

#### 3. Candidate Matching
```http
POST /match_making/{job_vacancy_id}/{schema}/{recalculate_all}
```
Performs advanced candidate-job matching.

#### 4. Get Job Configuration
```http
GET /job_vacancy/{job_vacancy_id}/{schema}/config
```
Retrieves scoring and matching configuration for a job.

#### 5. API Statistics
```http
GET /stats
```
Returns API usage statistics and system metrics.

### Response Format

All API responses follow a consistent structure:

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    "results": [...],
    "summary": {
      "total_processed": 150,
      "processing_time_seconds": 2.34
    }
  },
  "metadata": {
    "pagination": {...}
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_abc123"
}
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
Rscript tests/testthat.R

# Run specific test file
Rscript -e "testthat::test_file('tests/testthat/test-scoring-algorithms.R')"

# Run tests with coverage
Rscript -e "covr::package_coverage()"
```

### Test Structure

- **Unit Tests**: Test individual functions and algorithms
- **Integration Tests**: Test component interactions
- **API Tests**: Test endpoint functionality
- **Performance Tests**: Test system performance under load

### Writing Tests

```r
test_that("scoring algorithm works correctly", {
  # Arrange
  education <- "S2"
  config <- list(...)
  
  # Act
  result <- calculate_education_score(education, config)
  
  # Assert
  expect_equal(result, 4)
  expect_true(result >= 0 && result <= 5)
})
```

## 🚀 Deployment

### Docker Deployment

1. **Build the image**
   ```bash
   docker build -t candidate-scoring-api .
   ```

2. **Run the container**
   ```bash
   docker run -d \
     --name candidate-api \
     -p 5656:5656 \
     --env-file .env \
     candidate-scoring-api
   ```

### Production Deployment

1. **Environment Setup**
   - Set `APP_ENV=production`
   - Configure production database connections
   - Set up proper logging and monitoring
   - Configure reverse proxy (nginx/Apache)

2. **Security Considerations**
   - Use strong authentication tokens
   - Enable HTTPS
   - Configure firewall rules
   - Regular security updates

3. **Monitoring**
   - Set up log aggregation
   - Configure health check monitoring
   - Set up performance metrics collection
   - Configure alerting

## 💻 Development

### Project Structure

```
├── R/                          # Source code
│   ├── api/                   # API layer
│   ├── services/              # Business logic
│   ├── repositories/          # Data access
│   ├── models/                # Data models
│   ├── utils/                 # Utilities
│   ├── validators/            # Input validation
│   └── config/                # Configuration
├── tests/                     # Test suite
├── docs/                      # Documentation
├── logs/                      # Application logs
├── queries/                   # SQL queries
├── api.R                      # API definition
├── run.R                      # Application entry point
├── Dockerfile                 # Container definition
└── renv.lock                  # Dependency lock file
```

### Development Workflow

1. **Create feature branch**
   ```bash
   git checkout -b feature/new-feature
   ```

2. **Write tests first** (TDD approach)
   ```r
   # Write failing tests
   # Implement functionality
   # Ensure tests pass
   ```

3. **Follow coding standards**
   - Use meaningful variable names
   - Write comprehensive documentation
   - Follow R style guide
   - Add error handling

4. **Run tests and checks**
   ```bash
   # Run tests
   Rscript tests/testthat.R
   
   # Check code style
   Rscript -e "lintr::lint_package()"
   ```

### Adding New Features

1. **Scoring Algorithms**: Add to `R/utils/scoring_algorithms.R`
2. **Matching Logic**: Add to `R/utils/matching_algorithms.R`
3. **API Endpoints**: Add to `R/api/controllers/`
4. **Data Models**: Add to `R/models/`
5. **Tests**: Add corresponding test files

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Write tests for new functionality
4. Implement the feature
5. Ensure all tests pass
6. Submit a pull request

### Code Style

- Follow the [tidyverse style guide](https://style.tidyverse.org/)
- Use meaningful function and variable names
- Write comprehensive documentation
- Include error handling
- Add unit tests for new functionality

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation in the `docs/` directory

## 🔄 Changelog

### v2.0.0 (Current)
- Complete refactoring with layered architecture
- Enhanced security and validation
- Comprehensive testing suite
- Performance optimizations
- Production-ready deployment

### v1.0.0 (Legacy)
- Basic scoring and matching functionality
- Monolithic architecture
- Limited error handling
