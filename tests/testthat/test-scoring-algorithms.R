#' Test Scoring Algorithms
#' 
#' Comprehensive tests for all scoring algorithm functions
#' 
#' <AUTHOR> Team
#' @version 2.0.0

library(testthat)

# Source the scoring algorithms
source("../../R/utils/scoring_algorithms.R")

test_that("calculate_education_score works correctly", {
  # Test with valid education levels
  expect_equal(calculate_education_score("S3", "Computer Science", NULL), 5)
  expect_equal(calculate_education_score("S2", "Engineering", NULL), 4)
  expect_equal(calculate_education_score("S1", "Business", NULL), 3)
  expect_equal(calculate_education_score("D4", "Technology", NULL), 2)
  expect_equal(calculate_education_score("D3", "Arts", NULL), 1)
  
  # Test with invalid inputs
  expect_equal(calculate_education_score(NULL, "Major", NULL), 0)
  expect_equal(calculate_education_score(NA, "Major", NULL), 0)
  expect_equal(calculate_education_score("", "Major", NULL), 0)
  expect_equal(calculate_education_score("INVALID", "Major", NULL), 0)
  
  # Test case insensitivity
  expect_equal(calculate_education_score("s3", "Major", NULL), 5)
  expect_equal(calculate_education_score(" S2 ", "Major", NULL), 4)
})

test_that("calculate_experience_score works correctly", {
  # Test with valid experience values
  expect_equal(calculate_experience_score(6, "Technology", NULL), 5)
  expect_equal(calculate_experience_score(4, "Finance", NULL), 4)
  expect_equal(calculate_experience_score(2.5, "Healthcare", NULL), 3)
  expect_equal(calculate_experience_score(1.5, "Education", NULL), 2)
  expect_equal(calculate_experience_score(0.5, "Retail", NULL), 1)
  expect_equal(calculate_experience_score(0, "Any", NULL), 0)
  
  # Test with invalid inputs
  expect_equal(calculate_experience_score(NULL, "Industry", NULL), 0)
  expect_equal(calculate_experience_score(NA, "Industry", NULL), 0)
  expect_equal(calculate_experience_score(-1, "Industry", NULL), 0)
  expect_equal(calculate_experience_score("invalid", "Industry", NULL), 0)
})

test_that("calculate_gpa_score works correctly", {
  # Test with valid GPA values for different education levels
  expect_equal(calculate_gpa_score(3.8, "S3", NULL), 5)
  expect_equal(calculate_gpa_score(3.4, "S2", NULL), 4)
  expect_equal(calculate_gpa_score(3.1, "S1", NULL), 3)
  expect_equal(calculate_gpa_score(2.8, "S1", NULL), 2)
  expect_equal(calculate_gpa_score(2.6, "S1", NULL), 1)
  expect_equal(calculate_gpa_score(2.4, "S1", NULL), 0)
  
  # Test with invalid inputs
  expect_equal(calculate_gpa_score(NULL, "S1", NULL), 0)
  expect_equal(calculate_gpa_score(NA, "S1", NULL), 0)
  expect_equal(calculate_gpa_score(-1, "S1", NULL), 0)
  expect_equal(calculate_gpa_score(5, "S1", NULL), 0)  # GPA > 4.0
})

test_that("calculate_age_score works correctly", {
  # Test with valid age values
  expect_equal(calculate_age_score(25, NULL), 5)  # Optimal age range
  expect_equal(calculate_age_score(33, NULL), 4)
  expect_equal(calculate_age_score(38, NULL), 3)
  expect_equal(calculate_age_score(43, NULL), 2)
  expect_equal(calculate_age_score(20, NULL), 2)
  expect_equal(calculate_age_score(50, NULL), 1)
  
  # Test with invalid inputs
  expect_equal(calculate_age_score(NULL, NULL), 0)
  expect_equal(calculate_age_score(NA, NULL), 0)
  expect_equal(calculate_age_score(-5, NULL), 0)
  expect_equal(calculate_age_score(150, NULL), 0)
})

test_that("calculate_toefl_score works correctly", {
  # Test with valid TOEFL scores
  expect_equal(calculate_toefl_score(620, NULL), 5)
  expect_equal(calculate_toefl_score(570, NULL), 4)
  expect_equal(calculate_toefl_score(520, NULL), 3)
  expect_equal(calculate_toefl_score(470, NULL), 2)
  expect_equal(calculate_toefl_score(420, NULL), 1)
  expect_equal(calculate_toefl_score(350, NULL), 0)
  
  # Test with invalid inputs
  expect_equal(calculate_toefl_score(NULL, NULL), 0)
  expect_equal(calculate_toefl_score(NA, NULL), 0)
  expect_equal(calculate_toefl_score(-100, NULL), 0)
  expect_equal(calculate_toefl_score(800, NULL), 0)  # Score too high
})

test_that("calculate_weighted_score works correctly", {
  # Test with valid scores and default weights
  result <- calculate_weighted_score(5, 4, 3, 2, 1, NULL)
  expect_true(is.numeric(result))
  expect_true(result >= 0 && result <= 100)
  
  # Test with custom weights
  custom_weights <- list(
    education = 30,
    experience = 30,
    gpa = 20,
    age = 10,
    toefl = 10
  )
  result <- calculate_weighted_score(5, 5, 5, 5, 5, custom_weights)
  expect_equal(result, 100)
  
  # Test with zero scores
  result <- calculate_weighted_score(0, 0, 0, 0, 0, NULL)
  expect_equal(result, 0)
  
  # Test with invalid inputs
  result <- calculate_weighted_score(NULL, NA, "invalid", -1, 6, NULL)
  expect_true(is.numeric(result))
  expect_true(result >= 0 && result <= 100)
})

test_that("determine_pass_status works correctly", {
  # Test with valid scores and cutoffs
  expect_equal(determine_pass_status(80, 40), "PASSED")
  expect_equal(determine_pass_status(40, 40), "PASSED")  # Equal to cutoff
  expect_equal(determine_pass_status(39, 40), "NOT PASSED")
  expect_equal(determine_pass_status(0, 40), "NOT PASSED")
  
  # Test with invalid inputs
  expect_equal(determine_pass_status(NULL, 40), "NOT PASSED")
  expect_equal(determine_pass_status(NA, 40), "NOT PASSED")
  expect_equal(determine_pass_status(50, NULL), "NOT PASSED")  # Uses default cutoff
  expect_equal(determine_pass_status("invalid", 40), "NOT PASSED")
})

test_that("scoring algorithms handle edge cases", {
  # Test with boundary values
  expect_equal(calculate_education_score("S3", "", NULL), 5)
  expect_equal(calculate_experience_score(0.001, "", NULL), 1)
  expect_equal(calculate_gpa_score(4.0, "S1", NULL), 5)
  expect_equal(calculate_age_score(18, NULL), 2)
  expect_equal(calculate_toefl_score(677, NULL), 5)  # Max TOEFL score
  
  # Test with extreme values
  expect_equal(calculate_experience_score(100, "Industry", NULL), 5)  # Very high experience
  expect_equal(calculate_age_score(65, NULL), 1)  # Older age
})

test_that("scoring algorithms are consistent", {
  # Test that same inputs produce same outputs
  for (i in 1:10) {
    score1 <- calculate_education_score("S2", "Engineering", NULL)
    score2 <- calculate_education_score("S2", "Engineering", NULL)
    expect_equal(score1, score2)
  }
  
  # Test that weighted scores are deterministic
  for (i in 1:10) {
    score1 <- calculate_weighted_score(5, 4, 3, 2, 1, NULL)
    score2 <- calculate_weighted_score(5, 4, 3, 2, 1, NULL)
    expect_equal(score1, score2)
  }
})

test_that("scoring algorithms handle configuration objects", {
  # Test with mock configuration
  mock_config <- list(
    norma_education = data.frame(
      LastEducation = c("S3", "S2", "S1"),
      Score = c(10, 8, 6)
    )
  )
  
  # This would test configuration-based scoring
  # For now, just ensure it doesn't error
  expect_no_error(calculate_education_score("S2", "Major", mock_config))
})
