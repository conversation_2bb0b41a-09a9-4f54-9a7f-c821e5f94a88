#' Test Input Validators
#' 
#' Comprehensive tests for input validation functions
#' 
#' <AUTHOR> Team
#' @version 2.0.0

library(testthat)

# Source the validators
source("../../R/validators/input_validators.R")

test_that("ValidationResult class works correctly", {
  # Test initialization
  result <- ValidationResult$new()
  expect_true(result$valid)
  expect_equal(length(result$errors), 0)
  expect_equal(length(result$warnings), 0)
  
  # Test adding errors
  result$add_error("field1", "Error message")
  expect_false(result$valid)
  expect_equal(length(result$errors), 1)
  expect_true(result$has_errors())
  
  # Test adding warnings
  result$add_warning("field2", "Warning message")
  expect_equal(length(result$warnings), 1)
  expect_true(result$has_warnings())
  
  # Test getting messages
  error_messages <- result$get_error_messages()
  expect_equal(length(error_messages), 1)
  expect_true(grepl("field1", error_messages[1]))
  
  warning_messages <- result$get_warning_messages()
  expect_equal(length(warning_messages), 1)
  expect_true(grepl("field2", warning_messages[1]))
})

test_that("validate_job_vacancy_id works correctly", {
  # Test valid job vacancy IDs
  result <- validate_job_vacancy_id(123)
  expect_true(result$valid)
  expect_false(result$has_errors())
  
  result <- validate_job_vacancy_id("456")
  expect_true(result$valid)
  
  # Test invalid job vacancy IDs
  result <- validate_job_vacancy_id(NULL)
  expect_false(result$valid)
  expect_true(result$has_errors())
  
  result <- validate_job_vacancy_id(NA)
  expect_false(result$valid)
  
  result <- validate_job_vacancy_id(0)
  expect_false(result$valid)
  
  result <- validate_job_vacancy_id(-5)
  expect_false(result$valid)
  
  result <- validate_job_vacancy_id("invalid")
  expect_false(result$valid)
  
  result <- validate_job_vacancy_id(2147483648)  # Too large
  expect_false(result$valid)
})

test_that("validate_schema works correctly", {
  # Test valid schema names
  result <- validate_schema("public")
  expect_true(result$valid)
  
  result <- validate_schema("tenant1")
  expect_true(result$valid)
  
  result <- validate_schema("development")
  expect_true(result$valid)
  
  result <- validate_schema("test_schema_123")
  expect_true(result$valid)
  
  # Test invalid schema names
  result <- validate_schema(NULL)
  expect_false(result$valid)
  
  result <- validate_schema("")
  expect_false(result$valid)
  
  result <- validate_schema("   ")
  expect_false(result$valid)
  
  # Test dangerous patterns
  result <- validate_schema("drop_table")
  expect_false(result$valid)
  
  result <- validate_schema("schema'; DROP TABLE users; --")
  expect_false(result$valid)
  
  result <- validate_schema("schema with spaces")
  expect_false(result$valid)
  
  # Test length limit
  long_schema <- paste(rep("a", 70), collapse = "")
  result <- validate_schema(long_schema)
  expect_false(result$valid)
  
  # Test unknown schema (should have warning)
  result <- validate_schema("unknown_schema")
  expect_true(result$valid)  # Valid format
  expect_true(result$has_warnings())  # But has warning
})

test_that("validate_recalculate_all works correctly", {
  # Test valid boolean values
  result <- validate_recalculate_all(TRUE)
  expect_true(result$valid)
  
  result <- validate_recalculate_all(FALSE)
  expect_true(result$valid)
  
  result <- validate_recalculate_all("true")
  expect_true(result$valid)
  
  result <- validate_recalculate_all("false")
  expect_true(result$valid)
  
  result <- validate_recalculate_all(1)
  expect_true(result$valid)
  
  result <- validate_recalculate_all(0)
  expect_true(result$valid)
  
  # Test invalid values
  result <- validate_recalculate_all(NULL)
  expect_false(result$valid)
  
  result <- validate_recalculate_all(NA)
  expect_false(result$valid)
  
  result <- validate_recalculate_all("invalid")
  expect_false(result$valid)
  
  result <- validate_recalculate_all(2)
  expect_false(result$valid)
})

test_that("validate_api_request works correctly", {
  # Test valid request
  result <- validate_api_request(123, "public", TRUE)
  expect_true(result$valid)
  
  # Test with multiple errors
  result <- validate_api_request(NULL, "", "invalid")
  expect_false(result$valid)
  expect_true(length(result$errors) >= 3)  # Should have multiple errors
  
  # Test with warnings
  result <- validate_api_request(123, "unknown_schema", TRUE)
  expect_true(result$valid)
  expect_true(result$has_warnings())
})

test_that("validate_email works correctly", {
  # Test valid emails
  result <- validate_email("<EMAIL>")
  expect_true(result$valid)
  
  result <- validate_email("<EMAIL>")
  expect_true(result$valid)
  
  result <- validate_email("<EMAIL>")
  expect_true(result$valid)
  
  # Test invalid emails
  result <- validate_email(NULL)
  expect_false(result$valid)
  
  result <- validate_email("")
  expect_false(result$valid)
  
  result <- validate_email("invalid-email")
  expect_false(result$valid)
  
  result <- validate_email("@domain.com")
  expect_false(result$valid)
  
  result <- validate_email("user@")
  expect_false(result$valid)
  
  result <- validate_email("user@domain")
  expect_false(result$valid)
  
  # Test email too long
  long_email <- paste0(paste(rep("a", 250), collapse = ""), "@domain.com")
  result <- validate_email(long_email)
  expect_false(result$valid)
})

test_that("validate_numeric_range works correctly", {
  # Test valid numeric values
  result <- validate_numeric_range(50, "score", 0, 100)
  expect_true(result$valid)
  
  result <- validate_numeric_range(0, "minimum", 0, 100)
  expect_true(result$valid)
  
  result <- validate_numeric_range(100, "maximum", 0, 100)
  expect_true(result$valid)
  
  # Test out of range values
  result <- validate_numeric_range(-10, "score", 0, 100)
  expect_false(result$valid)
  
  result <- validate_numeric_range(150, "score", 0, 100)
  expect_false(result$valid)
  
  # Test invalid numeric values
  result <- validate_numeric_range("invalid", "score", 0, 100)
  expect_false(result$valid)
  
  result <- validate_numeric_range(NULL, "score", 0, 100, required = TRUE)
  expect_false(result$valid)
  
  result <- validate_numeric_range(NULL, "score", 0, 100, required = FALSE)
  expect_true(result$valid)
})

test_that("validate_string_length works correctly", {
  # Test valid string lengths
  result <- validate_string_length("hello", "name", 1, 10)
  expect_true(result$valid)
  
  result <- validate_string_length("a", "name", 1, 10)
  expect_true(result$valid)
  
  result <- validate_string_length("1234567890", "name", 1, 10)
  expect_true(result$valid)
  
  # Test invalid string lengths
  result <- validate_string_length("", "name", 1, 10)
  expect_false(result$valid)
  
  result <- validate_string_length("12345678901", "name", 1, 10)
  expect_false(result$valid)
  
  result <- validate_string_length(NULL, "name", 1, 10, required = TRUE)
  expect_false(result$valid)
  
  result <- validate_string_length(NULL, "name", 1, 10, required = FALSE)
  expect_true(result$valid)
})

test_that("sanitize_input works correctly", {
  # Test basic sanitization
  result <- sanitize_input("  hello world  ")
  expect_equal(result, "hello world")
  
  # Test HTML removal
  result <- sanitize_input("<script>alert('xss')</script>hello")
  expect_equal(result, "hello")
  
  result <- sanitize_input("<p>Hello <b>world</b></p>")
  expect_equal(result, "Hello world")
  
  # Test null byte removal
  result <- sanitize_input("hello\0world")
  expect_equal(result, "helloworld")
  
  # Test with HTML allowed
  result <- sanitize_input("<p>Hello <b>world</b></p>", allow_html = TRUE)
  expect_true(grepl("<p>", result))
  expect_true(grepl("<b>", result))
  
  # Test with NULL and NA
  expect_equal(sanitize_input(NULL), NULL)
  expect_equal(sanitize_input(NA), NA)
})

test_that("validators handle edge cases", {
  # Test with extreme values
  result <- validate_job_vacancy_id(.Machine$integer.max)
  expect_false(result$valid)  # Too large
  
  # Test with special characters in schema
  result <- validate_schema("schema_with_üñíçødé")
  expect_false(result$valid)  # Non-ASCII characters
  
  # Test with very long strings
  long_string <- paste(rep("a", 1000), collapse = "")
  result <- validate_string_length(long_string, "field", 1, 500)
  expect_false(result$valid)
  
  # Test with floating point precision
  result <- validate_numeric_range(99.999999, "score", 0, 100)
  expect_true(result$valid)
})
