#' Test Suite Entry Point
#' 
#' This file sets up the testing environment and runs all tests
#' 
#' <AUTHOR> Team
#' @version 2.0.0

library(testthat)
library(logger)

# Set up test environment
log_threshold(WARN)  # Reduce log noise during testing

# Set test environment variables
Sys.setenv(
  APP_ENV = "test",
  DB_NAME_READ = "test_db",
  DB_HOST_READ = "localhost",
  DB_PORT_READ = "5432",
  DB_USER_READ = "test_user",
  DB_PASSWORD_READ = "test_password",
  DB_NAME_WRITE = "test_db",
  DB_HOST_WRITE = "localhost",
  DB_PORT_WRITE = "5432",
  DB_USER_WRITE = "test_user",
  DB_PASSWORD_WRITE = "test_password",
  AWS_ACCESS_KEY_ID = "test_key",
  AWS_SECRET_ACCESS_KEY = "test_secret",
  AWS_DEFAULT_REGION = "us-east-1",
  VALID_TOKEN = "test_token_123"
)

# Run all tests
test_check("scoring-candidate")
